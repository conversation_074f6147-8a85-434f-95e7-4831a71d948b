import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart'; 
import '../widgets/clay_progress_bar.dart';
import '../controllers/kyc_controller.dart'; 
import '../widgets/clay_button.dart';

class ReviewSubmitPage extends StatelessWidget {
  final KYCController controller = Get.find<KYCController>();

    ReviewSubmitPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
persistentFooterButtons: [
  _buildSubmitButton(theme),
],
      persistentFooterAlignment:AlignmentDirectional.center,
      appBar: AppBar(
        title: const Text('Review & Submit'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() => Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
           const  ClayProgress(
              currentStep: 5,
              totalSteps: 5,
            ),
            const SizedBox(height: 30),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDocumentPreview(
                      'Front ID',
                      controller.frontID.value,
                    ),
                    _buildDocumentPreview(
                      'Back ID',
                      controller.backID.value,
                    ),
                    _buildDocumentPreview(
                      'Selfie',
                      controller.selfie.value,
                    ),
                    const SizedBox(height: 25),
                    _buildIdNumberSection(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            
          ],
        ),
      )),
    );
  }

  Widget _buildDocumentPreview(String title, File? file) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 10),
        Container(
          height: 150,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: Theme.of(Get.context!).colorScheme.surface,
          ),
          child: file != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.file(file, fit: BoxFit.cover),
                )
              : Center(
                  child: Text(
                    'No $title captured',
                    style: TextStyle(
                      color: Theme.of(Get.context!)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.4),
                    ),
                  ),
                ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildIdNumberSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ID Number',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 10),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(Get.context!).colorScheme.surface,
            borderRadius: BorderRadius.circular(15),
          ),
          child: Text(
            controller.idNumber.text,
            style: const TextStyle(fontSize: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(ThemeData theme) {
    return Obx(() {
      if (controller.isUploading.value) {
        return Column(
          children: [
            CircularProgressIndicator(
              value: controller.uploadProgress.value,
              backgroundColor: theme.colorScheme.surface,
            ),
            const SizedBox(height: 10),
            Text(
              'Uploading ${controller.currentUpload.value}...',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
          ],
        );
      }

      return ClayButton(
        text: 'Submit KYC',
        icon: Icons.cloud_upload,
        onPressed: () {
          if (!controller.allImagesCaptured) {
            Get.snackbar(
              'Incomplete Submission',
              'Please capture all required documents',
              snackPosition: SnackPosition.bottom,
            );
            return;
          }

          showDialog(
            context: Get.context!,
            builder: (context) => AlertDialog(
              title: const Text('Confirm Submission'),
              content: const Text(
                  'Are you sure you want to submit your KYC information?'),
              actions: [
                TextButton(
                  onPressed: Get.back,
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Get.back();
                    controller.uploadImages();
                  },
                  child: const Text('Submit'),
                ),
              ],
            ),
          );
        },
      );
    });
  }
}