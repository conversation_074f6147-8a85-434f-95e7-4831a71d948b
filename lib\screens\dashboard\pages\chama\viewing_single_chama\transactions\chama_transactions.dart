// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart'; 
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/view_transaction_details.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/main.dart' show isLight;
import '../../../../../../utils/utils_exports.dart';

class ChamaTransactionsPage extends StatefulWidget {
  final bool isFullPage;
  final bool isFromMemberTransactions;
  final String? accountNo;
  final ChamaMembers? member;

  const ChamaTransactionsPage(
      {super.key,
      required this.isFullPage,
      this.accountNo,
      this.member,
      required this.isFromMemberTransactions});

  @override
  State<ChamaTransactionsPage> createState() => _ChamaTransactionsPageState();
}

class _ChamaTransactionsPageState extends State<ChamaTransactionsPage> {
  TextEditingController searchController = TextEditingController();
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final dateformat = DateFormat('EE, dd MMMM');
  List<Transaction> filterByName = [];
  final ChamaController chamaController = Get.put(ChamaController());
  bool singleTrans = false;
  List<String> dropdownItemList = ["code", "Date", "Account No", ];
  // List<String> categoryFilter = ["ALL", "PENALTY", "CONTRIBUTION"];
  String selectedCategoryFilter = "ALL";
  String selectedFilter = "";
  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();
  TextEditingController accountController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController categoryController = TextEditingController();

  @override
  void initState() {
    super.initState();
    
WidgetsBinding.instance.addPostFrameCallback((_) {
  chamaController.chamaTransactions.clear();
});

    filterByName = chamaController.chamaTransactions;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:   widget.isFullPage
    ? AppBar( 
        elevation: 0,
        leadingWidth: 120.w,
        leading: GestureDetector( 
           onTap: () => Navigator.pop(context),
          child: Row(
            children: [
              Icon(Icons.navigate_before),
              SizedBox(width: 4.w)
              ,Text('Back')
            ],
          ),
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(90.h),
          child:   ColoredBox( 
            color: Theme.of(context).scaffoldBackgroundColor,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [SizedBox(width: 12.w),
                  Expanded(
                        child: TextField(
                          controller: searchController,
                          onChanged: (value) {
                            // Update filterByName with the latest data from chamaController
                            if (value.isEmpty) {
                              filterByName = List.from(chamaController.chamaTransactions);
                            } else {
                              filterByName = chamaController.chamaTransactions
                                  .where((p0) => (p0.firstName?.toLowerCase() ?? '')
                                      .contains(value.toLowerCase()))
                                  .toList();
                            }
                            setState(() {});
                          },
                          decoration: InputDecoration(
                            hintText: "Search transactions...",
                            hintStyle: TextStyle(
                              color: Theme.of(context).hintColor,
                              fontSize: 14.sp,
                            ),
                            filled: true,
                            fillColor: Theme.of(context).scaffoldBackgroundColor,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: BorderSide(
                                // color: Theme.of(context).colorScheme.primary,
                                width: 1,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.search_rounded,
                              color: Theme.of(context).hintColor,
                              size: 24.h,
                            ),
                            suffixIcon: searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: Icon(Icons.clear_rounded,
                                        color: Theme.of(context).hintColor),
                                    onPressed: () => searchController.clear(),
                                  )
                                : null,
                            contentPadding: EdgeInsets.symmetric(vertical: 12.h),
                          ),
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ),
              
                  SizedBox(width: 12.w),
                  CustomDropDown(
                    // fillColor: Theme.of(context).colorScheme.surfaceVariant,
                    width: 150.w,
                    hintText: "Filter",
                    hintStyle: TextStyle(
                      fontSize: 12.h,
                      // color: Theme.of(context).colorScheme.primary,
                    ),
                    items: dropdownItemList,
                    prefix: Icon(
                      Icons.filter_alt_rounded,
                      // color: Theme.of(context).colorScheme.primary,
                      size: 20.h,
                    ),
                    onChanged: (value) {
                      setState(() => selectedFilter = value);
                    },
                    // borderRadius: 16,
                  ),
                SizedBox(width: 12.w),  ],
              ),
            ),
          ),
        ),
        title: Text('Transactions'),
      )
    : AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 1,
        titleSpacing: 0,
        title: Text(
          widget.isFromMemberTransactions
              ? "${widget.member?.firstName} ${widget.member?.secondName}'s Transactions"
              : "Recent Transactions",
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
        ),
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 16.w),
            child: TextButton.icon(
              style: FilledButton.styleFrom(
                // backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
                
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 8.h,
                ),
              ),
              onPressed: widget.isFromMemberTransactions
                  ? null
                  : () => Get.to(() => ChamaTransactionsPage(
                        isFullPage: true,
                        isFromMemberTransactions: false,
                      )),
              icon: Icon(
                Icons.open_in_new_rounded,
                size: 18.h,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
              label: Text(
                "See all",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
            ),
          ),
        ],
      ),


      
      body: Container(
        margin: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
           
            if (widget.isFullPage && selectedFilter == "Account No")
              _buildAccountFilterUI(),
            if (widget.isFullPage && selectedFilter == "code")
              _buildCodeChamaFilterUI(),
            if (widget.isFullPage && selectedFilter == "Date")
              _buildDateChamaFilterUI(),
            // if (widget.isFullPage && selectedFilter == "category")
            //   _buildCategoryChamaFilterUI(),
            if (widget.isFullPage && selectedFilter == "") SizedBox(),
            _buildAllTransacions(),

            //startDate.text.isNotEmpty && endDate.text.isNotEmpty || accountController.text.isNotEmpty || codeController.text.isNotEmpty || categoryController.text.isNotEmpty ?
            ],
        ),
      ),
      bottomNavigationBar:  Container(
        height: 70.h,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.inversePrimary,
              ),
              child: Obx(() {
                if (chamaController.chamaTransactions.isNotEmpty &&
                    chamaController.results.value!.totalPages! > 0) {
                  return Visibility(
                    visible: true,
                    child: NumberPaginator(
                      numberPages:
                          chamaController.results.value!.totalPages ?? 0,
                      onPageChange: (int index) async {
                        await chamaController.getChamaTrnsactions(
                          chamaId:
                              chamaDataController.chama.value.chama?.id ?? 0,
                          page: index,
                        );
                        setState(() {});
                      },
                    ),
                  );
                } else {
                  return Container(); // Empty container when transactions is empty
                }
              }),
            )
          ,
      floatingActionButton: ExportButton(singleTrans: singleTrans),
      floatingActionButtonLocation: FloatingActionButtonLocation.endContained,
    );
  }

  //ALL TRANSACTIONS
  Widget _buildAllTransacions() {
    return GetX(
        init: ChamaController(),
        initState: (state) {
          Future.delayed(Duration.zero, () async {
            try {
              await state.controller?.getChamaTrnsactions(
                chamaId: chamaDataController.chama.value.chama?.id ?? 0,
                accountNo:
                    widget.isFromMemberTransactions ? widget.accountNo : null,
              );
              // ignore: empty_catches
            } catch (e) {}
          });
        },
        builder: (ChamaController chamaController) {
          if (chamaController.isGetChamaTransactionLoading.isTrue) {
            return SizedBox(
              height: SizeConfig.screenHeight * .1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: 4.sp,
                      size: 40.0.sp,
                    ),
                    const Text(
                      "loading..",
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            );
          } else if (chamaController.chamaTransactions.isEmpty) {
            return SizedBox(
              height: 200.h,
              child: Center(child: Text("No transactions yet")));
          } else if (filterByName.isEmpty) {
            return  SizedBox(
              height: 200.h,child: Center(child: Text("Transaction not found")));
          } else if (filterByName.isNotEmpty) {
            return Expanded(
              child: GroupedListView<Transaction, DateTime>(
                sort: false,
                useStickyGroupSeparators: true,
                elements: filterByName,
                groupBy: (Transaction element) {
                  DateTime date = element.createdAt!.toLocal();
                  return DateTime(date.year, date.month, date.day);
                },
                //groupSeparatorBuilder: (value) => Container(decoration: BoxDecoration(color: Colors.red,border: Border.all(color: AppColors.blueButtonColor)),),
                groupHeaderBuilder: (value) {
                  final date = dateformat.format(value.createdAt!.toLocal());
                  return Container(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      date,
                      style:   TextStyle(
                        color: isLight.value ? Colors.black : Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 17.0,
                      ),
                    ),
                  );
                },
                itemBuilder: (context, Transaction transaction) {
                  return SingleChamaTransaction(item: transaction);
                },

                separator: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                ),
              ),
            );
          }
          return Text("No transaction yet");
        });
  }

  Widget _buildAccountFilterUI() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Expanded(
            child: CustomSearchView(
              controller: accountController,
              hintText: "Filter by phone number",
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              onChanged: (p0) => _updateFilter(),
            ),
          ),
          SizedBox(width: 12.w),
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: () {
                setState(() {
                  selectedFilter = "Filter";
                  accountController.clear();
                });
              },
              child: Padding(
                padding: EdgeInsets.all(8.h),
                child: CustomImageView(
                  imagePath: AssetUrl.imgIconoirCancel,
                  height: 24.h,
                  width: 24.w,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  //TRANSACTION CODE FILTER
  Widget _buildCodeChamaFilterUI() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Expanded(
            child: CustomSearchView(
              controller: codeController,
              hintText: "Filter by transaction code",
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              onChanged: (p0) => _updateFilter(),
            ),
          ),
          SizedBox(width: 12.w),
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: () {
                setState(() {
                  selectedFilter = "Filter";
                  codeController.clear();
                });
              },
              child: Padding(
                padding: EdgeInsets.all(8.h),
                child: CustomImageView(
                  imagePath: AssetUrl.imgIconoirCancel,
                  height: 24.h,
                  width: 24.w,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  //DATE CHAMA FILTER
  Widget _buildDateChamaFilterUI() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Expanded(
            child: TextFormField(
              controller: startDate,
              style: TextStyle(fontSize: 14.sp),
              readOnly: true,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );

                if (pickedDate != null) {
                  startDate.text = DateFormat('yyyy-MM-dd').format(pickedDate);
                }
              },
              decoration: InputDecoration(
                labelText: 'Start Date',
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).dividerColor),
                ),
                suffixIcon: Icon(Icons.calendar_today, size: 20.h),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: TextFormField(
              controller: endDate,
              style: TextStyle(fontSize: 14.sp),
              readOnly: true,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );

                if (pickedDate != null) {
                  endDate.text = DateFormat('yyyy-MM-dd').format(pickedDate);
                  _updateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'End Date',
                contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: Theme.of(context).dividerColor),
                ),
                suffixIcon: Icon(Icons.calendar_today, size: 20.h),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: () {
                setState(() {
                  selectedFilter = "Filter";
                  startDate.clear();
                  endDate.clear();
                });
              },
              child: Padding(
                padding: EdgeInsets.all(8.h),
                child: CustomImageView(
                  imagePath: AssetUrl.imgIconoirCancel,
                  height: 24.h,
                  width: 24.w,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  //CATEGORY CHAMA FILTER
  // Widget _buildCategoryChamaFilterUI() {
  //   return Padding(
  //     padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
  //     child: SizedBox(
  //       height: 40.h,
  //       child: ListView.separated(
  //         scrollDirection: Axis.horizontal,
  //         itemCount: categoryFilter.length,
  //         separatorBuilder: (context, index) => SizedBox(width: 8.w),
  //         itemBuilder: (context, index) {
  //           final category = categoryFilter[index];
  //           final isSelected = selectedCategoryFilter == category;
            
  //           return Material(
  //             color: Colors.transparent,
  //             child: InkWell(
  //               borderRadius: BorderRadius.circular(20),
  //               onTap: () => onSelectCategoryFilter(category),
  //               child: Container(
  //                 padding: EdgeInsets.symmetric(horizontal: 16.w),
  //                 alignment: Alignment.center,
  //                 decoration: BoxDecoration(
  //                   color: isSelected
  //                       ? Theme.of(context).colorScheme.primary
  //                       : Theme.of(context).colorScheme.primary.withOpacity(0.1),
  //                   borderRadius: BorderRadius.circular(20),
  //                 ),
  //                 child: Text(
  //                   category,
  //                   style: TextStyle(
  //                     fontSize: 14.sp,
  //                     fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
  //                     color: isSelected
  //                         ? Theme.of(context).colorScheme.onPrimary
  //                         : Theme.of(context).colorScheme.primary,
  //                   ),
  //                 ),
  //               ),
  //             ),
  //           );
  //         },
  //       ),
  //     ),
  //   );
  // }

  void onSelectCategoryFilter(String category) {
    setState(() {
      selectedCategoryFilter = category;
    });
    _updateFilter();
  }

  void _updateFilter() {
    setState(() {
      // Clear existing data before fetching new data
      filterByName = [];
      _fetchFilteredChamaTransactions();
    });
  }

  void _fetchFilteredChamaTransactions() async {
    try {
      await Get.find<ChamaController>().getChamaTrnsactions(
          chamaId: chamaDataController.chama.value.chama?.id ?? 0,
          startDate: startDate.text.trim(),
          endDate: endDate.text.trim(),
          accountNo: accountController.text.trim(),
          code: codeController.text.trim(),
          category: selectedCategoryFilter);
      setState(() {
        Get.find<ChamaController>().isGetChamaTransactionLoading(false);
      });
    } catch (e) {
      rethrow;
    }
  }
}

class ExportButton extends StatelessWidget {
  final bool singleTrans;
  const ExportButton({super.key, required this.singleTrans});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 60.0),
      child: CustomElevatedButton(
        buttonStyle: ButtonStyle(
          backgroundColor: WidgetStateProperty.all<Color>(
              const Color.fromARGB(255, 184, 129, 57)),
        ),
        width: 100.w,
        height: 45.h,
        text: "Export",
        buttonTextStyle: TextStyle(fontSize: 14.h, fontWeight: FontWeight.bold),
        leftIcon: Container(
            margin: EdgeInsets.only(right: 1.w),
            child: CustomImageView(
                imagePath: AssetUrl.expIcon, height: 14.h, width: 14.w)),
        onPressed: () {
          showModalBottomSheet(
            context: context,
            builder: (BuildContext context) {
              return ExportChamaContentWidget(singleTrans: singleTrans);
            },
          );
        },
        alignment: Alignment.bottomRight,
      ),
    );
  }
}