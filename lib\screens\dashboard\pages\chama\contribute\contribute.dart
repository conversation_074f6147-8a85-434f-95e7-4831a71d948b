import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';

import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';

import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';

import 'package:onekitty/models/chama/chama_contribute_request.dart';

import 'package:onekitty/screens/dashboard/pages/contribution/cardPayment.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/confirm_payment_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';

import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import '../../../../../utils/utils_exports.dart';

class ChamaContributePage extends StatefulWidget {
  const ChamaContributePage({super.key});

  @override
  State<ChamaContributePage> createState() => _ChamaContributePageState();
}

class _ChamaContributePageState extends State<ChamaContributePage> {
  final payerController = TextEditingController();
  final chamaMemberController = TextEditingController();
  final amtController = TextEditingController();
  final emailController = TextEditingController();
  final selectedChannel = "M-Pesa".obs;
  final dataControllerchama = Get.find<ChamaDataController>();
  final chamaController = Get.put(ChamaController());
  final kittyController = Get.put(KittyController());
  final eventController = Get.put(Eventcontroller());
  final formKey = GlobalKey<FormState>();
  final box = GetStorage();
  final myPhone = RxString('');
  final chamaMemberNo = RxString('');
  late PhoneNumber num =
      PhoneNumber(isoCode: 'KE', dialCode: '+254', phoneNumber: '');
  final isPayingPenalty = false.obs;
  final selectedPenaltyId = 0.obs;
  final contributionType = Rx<String?>(null);

  contributeAndPayPenalties(bool isPenalty,
      {int? penaltyId, int? penaltyAmt}) async {
    if (formKey.currentState!.validate()) {
      final channelCode =
          kittyController.getNetworkCode(networkTitle: selectedChannel.value);

      // Format phone numbers correctly for API request - ensure it has country code
      String formattedPayerPhone;
      String formattedAccountNumber;

      // For payer phone (the one making payment)
      if (myPhone.value.isEmpty) {
        Snack.show(false, "Please enter payer phone number");
        return false;
      }

      if (myPhone.value.startsWith('0')) {
        // Convert local format to full international without +
        formattedPayerPhone = "254${myPhone.value.substring(1)}";
      } else if (myPhone.value.startsWith('+254')) {
        // Already has international format, remove +
        formattedPayerPhone = myPhone.value.substring(1);
      } else if (myPhone.value.startsWith('254')) {
        // Already has country code without +
        formattedPayerPhone = myPhone.value;
      } else {
        // Handle case where number might be input without any prefix
        // First check if it's already 9 digits (standard KE number minus prefix)
        if (myPhone.value.length == 9) {
          formattedPayerPhone = "254${myPhone.value}";
        } else {
          // If it has some other format, assume it needs country code
          formattedPayerPhone = "254${myPhone.value}";
        }
      }

      // For account number (the member receiving contribution)
      if (chamaController.accountNo.value.isEmpty) {
        Snack.show(false, "Please enter chama member phone number");
        return false;
      }

      if (chamaController.accountNo.value.startsWith('0')) {
        // Convert local format to full international without +
        formattedAccountNumber =
            "254${chamaController.accountNo.value.substring(1)}";
      } else if (chamaController.accountNo.value.startsWith('+254')) {
        // Already has international format, remove +
        formattedAccountNumber = chamaController.accountNo.value.substring(1);
      } else if (chamaController.accountNo.value.startsWith('254')) {
        // Already has country code without +
        formattedAccountNumber = chamaController.accountNo.value;
      } else {
        // Handle case where number might be input without any prefix
        // First check if it's already 9 digits (standard KE number minus prefix)
        if (chamaController.accountNo.value.length == 9) {
          formattedAccountNumber = "254${chamaController.accountNo.value}";
        } else {
          // If it has some other format, assume it needs country code
          formattedAccountNumber = "254${chamaController.accountNo.value}";
        }
      }

      // Print for debugging
      print(
          "API Phone Numbers: Payer=$formattedPayerPhone, Account=$formattedAccountNumber");
      final UserKittyController _usercontroller =
          Get.put(UserKittyController());

      ChamaContributeRequest request = ChamaContributeRequest(
        kittyId: dataControllerchama.fetchKittyId.value,
        penaltyId: isPenalty ? penaltyId : null,
        userId: _usercontroller.getLocalUser()?.id,
        amount: int.parse(amtController.text.trim()),
        phoneNumber: formattedPayerPhone,
        accountNumber: formattedAccountNumber,
        latitude: box.read(CacheKeys.lat),
        longitude: box.read(CacheKeys.long),
        channelCode: channelCode,
        email: emailController.text,
        paymentType: isPenalty ? "PENALTY" : "CONTRIBUTION",
      );

      bool res = await chamaController.chamaContribute(
          request: request, isConfirm: false);
      if (res) {
        Snack.show(res, chamaController.apiMessage.string);
        showDialog(
            context: context,
            builder: (dialogContext) {
              return AlertDialog(
                title: Text(
                  chamaController.apiMessage.string.toUpperCase(),
                  style: context.titleText,
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("CHAMA TITLE: ${chamaController.chamaTitle.string}"),
                    Text("MEMBER: ${chamaController.memberNames.string}"),
                    Text("ACCOUNT NO: ${chamaController.ctrAccNo.string}"),
                    if (isPenalty)
                      Text("PENALTY: ${chamaController.penaltyName.string}")
                  ],
                ),
                actions: [
                  SizedBox(
                    height: 45,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text("CANCEL")),
                        Obx(() => CustomKtButton(
                            isLoading: chamaController
                                .isChamaContributionLoading.isTrue,
                            width: 120.w,
                            onPress: () async {
                              var resp = await chamaController.chamaContribute(
                                  request: request, isConfirm: true);
                              if (resp) {
                                if (channelCode == 0) {
                                  Snack.show(
                                      resp, chamaController.apiMessage.string);
                                  Navigator.pop(dialogContext);
                                  Get.off(() => const ProcessPaymentOtp(
                                      isChamaContribute: true));
                                }
                                if (channelCode == 63902 ||
                                    channelCode == 63903) {
                                  Snack.show(
                                      resp, chamaController.apiMessage.string);
                                  Navigator.pop(dialogContext);
                                }
                                if (channelCode == 55) {
                                  Get.off(() => const CardPayment(
                                      isChamaContribute: true));
                                }

                                // chamaController.memPenaltiesUsingAcc.clear();
                                amtController.clear();
                                payerController.clear();

                                Get.dialog(Container(
                                  height: SizeConfig.screenHeight * .4,
                                  width: SizeConfig.screenWidth * .8,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Dialog(
                                      shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(12.0)),
                                      ),
                                      child: SizedBox(
                                        child: Card(
                                            child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                chamaController
                                                    .apiMessage.string,
                                                style: context.titleText,
                                              ),
                                              const Divider(),
                                              Text(
                                                chamaController.contributeData[
                                                        "response_description"]
                                                    .toString(),
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w600,
                                                  fontSize: 16.sp,
                                                ),
                                              ),
                                              SizedBox(
                                                height: 10.h,
                                              ),
                                              Text(
                                                chamaController
                                                    .contributeData["detail"]
                                                    .toString(),
                                                style: TextStyle(
                                                  fontWeight: FontWeight.w400,
                                                  fontSize: 15.sp,
                                                ),
                                              ),
                                            ],
                                          ),
                                        )),
                                      )),
                                ));
                              } else {
                                if (!mounted) return;
                                Snack.show(
                                    resp, chamaController.apiMessage.string);
                              }
                            },
                            btnText: "CONFIRM"))
                      ],
                    ),
                  )
                ],
              );
            });
        //Get.offAndToNamed(NavRoutes.viewingSingleChama);
        return true;
      } else {
        Snack.show(res, chamaController.apiMessage.string);
        return false;
      }
    }
  }

  void showPenaltyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Select Penalty to Pay',
            style: context.titleText?.copyWith(fontSize: 18)),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 5,
                ),
                ...chamaController.memPenaltiesUsingAcc.map(
                  (penalty) => Container(
                    margin: const EdgeInsets.only(bottom: 5),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: AppColors.blueButtonColor)),
                    child: ListTile(
                      onTap: () {
                        selectedPenaltyId.value = penalty.id!;
                        amtController.text = penalty.amount.toString();
                        isPayingPenalty.value = true;
                        Navigator.pop(context);
                        contributeAndPayPenalties(
                          true,
                          penaltyId: selectedPenaltyId.value,
                        );
                      },
                      title: Text(penalty.title.toString()),
                      subtitle: Text(penalty.status.toString()),
                      trailing: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            FormattedCurrency()
                                .getFormattedCurrency(penalty.amount),
                            style: context.dividerTextSmall
                                ?.copyWith(color: Colors.green),
                          ),
                          Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 3),
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: AppColors.blueButtonColor),
                                  borderRadius: BorderRadius.circular(16)),
                              child: Text(
                                "Pay",
                                style: context.dividerTextLarge?.copyWith(
                                    color: AppColors.blueButtonColor),
                              ))
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  getChamaDetails() async {
    final arg = Get.parameters['id'];
    debugPrint('Kitty Id: $arg');
    String parsedKittyId = arg ?? '';
    int? kittyId = int.tryParse(parsedKittyId);
    if (kittyId == null) {
      dataControllerchama.apiMessage.value = "Invalid Kitty Id";
      return;
    }
    await dataControllerchama.getAllChamaDetailsWithKittyId(kittyId: kittyId);
    if (chamaController.accountNo.value.isNotEmpty) {
      String accNo = chamaController.accountNo.value;
      if (accNo.startsWith('0')) {
        accNo = accNo.substring(1);
      }
      await chamaController.getMemberPenaltiesUsingAcc(
        accNo: accNo,
        kittyId: dataControllerchama.fetchKittyId.value,
      );
    }
  }

  @override
  void initState() {
    super.initState();

    try {
      final loggedInUser = eventController.getLocalUser();
      String? userPhone = loggedInUser?.phoneNumber;

      if (userPhone?.isNotEmpty ?? false) {
        // Format for display in the text field (with leading 0)
        String formattedPhone = userPhone!;
        // Format for API with country code (254...)
        String internationalFormat;

        if (formattedPhone.startsWith('+254')) {
          // Already international format with +, remove + for API
          internationalFormat = formattedPhone.substring(1);
          formattedPhone = "0${formattedPhone.substring(4)}";
        } else if (formattedPhone.startsWith('254')) {
          // Already has country code without +
          internationalFormat = formattedPhone;
          formattedPhone = "0${formattedPhone.substring(3)}";
        } else if (formattedPhone.startsWith('0')) {
          // Has local format, convert to international
          internationalFormat = "254${formattedPhone.substring(1)}";
        } else {
          // No prefix, add leading 0 for display and 254 for API
          formattedPhone = "0$formattedPhone";
          internationalFormat = "254$formattedPhone";
        }

        num = PhoneNumber(
          isoCode: 'KE',
          dialCode: '+254',
          phoneNumber: formattedPhone,
        );

        // Set formatted numbers with proper formats
        payerController.text = formattedPhone;
        chamaMemberController.text = formattedPhone;

        // Store the full international format in the Rx variables
        myPhone.value = internationalFormat;
        chamaController.accountNo.value = internationalFormat;

        print(
            "Initialized phone numbers: Display=$formattedPhone, API=$internationalFormat");
      }

      emailController.text = loggedInUser?.email ?? "";
    } catch (e, stackTrace) {
      print("Error in initState: $e");
      print(stackTrace);
    }
  }

  @override
  void dispose() {
    payerController.dispose();
    chamaMemberController.dispose();
    amtController.dispose();
    emailController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        splitScreenMode: true,
        designSize: const Size(392.**************, 850.*************),
        builder: (context, child) {
          return Scaffold(
              appBar: AppBar(),
              body: GetX<ChamaDataController>(
                init: ChamaDataController(),
                initState: (state) async {
                  await getChamaDetails();
                },
                builder: (contr) => Container(
                  margin: const EdgeInsets.symmetric(horizontal: 20)
                      .copyWith(bottom: 5),
                  child: SingleChildScrollView(
                    child: Form(
                      key: formKey,
                      child: Column(
                        children: [
                          dataControllerchama.isloading.isTrue
                              ? const CircularProgressIndicator()
                              : dataControllerchama.status.isFalse
                                  ? Text(
                                      dataControllerchama.apiMessage.value,
                                      textAlign: TextAlign.center,
                                      style: context.titleText?.copyWith(
                                        fontSize: 22,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red,
                                      ),
                                    )
                                  : Text(
                                      dataControllerchama
                                              .chamaFromKitty.value.title ??
                                          "",
                                      textAlign: TextAlign.center,
                                      style: context.titleText?.copyWith(
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold),
                                    ),
                          const SizedBox(
                            height: 7,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                      "Created on: ${DateFormat("yyyy-MM-dd").format(dataControllerchama.chamaFromKitty.value.createdAt ?? DateTime.now())}"),
                                  Text(
                                      "End Date: ${DateFormat("yyyy-MM-dd").format(dataControllerchama.chamaFromKitty.value.deadline ?? DateTime.now())}"),
                                ],
                              ),
                              Text(
                                dataControllerchama
                                        .chamaFromKitty.value.status ??
                                    "",
                                style: context.dividerTextLarge
                                    ?.copyWith(color: Colors.green),
                              )
                            ],
                          ),
                          const Divider(),
                          SingleLineRow(
                            text: "Payer phone number",
                            popup: KtStrings.payerPhone,
                          ),
                          CustomInternationalPhoneInput(
                            onInputChanged: (
                              num,
                            ) {
                              // Store the full international format with country code
                              // The phone input gives us the full number with + prefix
                              // Remove the + character if it exists
                              String fullNumber = num.phoneNumber ?? '';
                              if (fullNumber.startsWith('+')) {
                                fullNumber = fullNumber.substring(1);
                              }
                              myPhone.value = fullNumber;
                              chamaMemberController.text = payerController.text;
                              chamaController.accountNo.value = fullNumber;
                            },
                            onInputValidated: (bool value) {},
                            ignoreBlank: false,
                            initialValue: num,
                            textFieldController: payerController,
                            formatInput: true,
                            keyboardType: const TextInputType.numberWithOptions(
                                signed: true, decimal: true),
                            inputBorder: const OutlineInputBorder(),
                            onSaved: (PhoneNumber number) {},
                          ),
                          SingleLineRow(
                            text: "Chama member phone number",
                            popup: KtStrings.chamaMemberPhNo,
                          ),
                          CustomInternationalPhoneInput(
                            onInputChanged: (
                              num,
                            ) {
                              // Store the full international format with country code
                              // The phone input gives us the full number with + prefix
                              // Remove the + character if it exists
                              String fullNumber = num.phoneNumber ?? '';
                              if (fullNumber.startsWith('+')) {
                                fullNumber = fullNumber.substring(1);
                              }
                              chamaController.accountNo.value = fullNumber;
                            },
                            onInputValidated: (bool value) {
                              if (value) {
                                // Format account number correctly for API call
                                // We need to extract just the number without country code for penalty check
                                String accNo = chamaController.accountNo.value;

                                // Create a local copy for API call, don't modify the original accountNo
                                String apiAccNo = accNo;
                                // take the last 9 digits
                                if (apiAccNo.length >= 9) {
                                  apiAccNo =
                                      apiAccNo.substring(apiAccNo.length - 9);
                                }
                                //append the country code
                                apiAccNo = "254$apiAccNo";
                                print(
                                    "Checking penalties for account: $apiAccNo");

                                chamaController.getMemberPenaltiesUsingAcc(
                                    accNo: apiAccNo,
                                    kittyId:
                                        dataControllerchama.fetchKittyId.value);
                              }
                            },
                            ignoreBlank: false,
                            initialValue: num,
                            textFieldController: chamaMemberController,
                            formatInput: true,
                            keyboardType: const TextInputType.numberWithOptions(
                                signed: true, decimal: true),
                            inputBorder: const OutlineInputBorder(),
                            onSaved: (PhoneNumber number) {},
                          ),
                          Align(
                              alignment: Alignment.centerLeft,
                              child: customText("Amount", context)),
                          CustomTextField(
                            controller: amtController,
                            labelText: "Amount",
                            isRequired: true,
                            showNoKeyboard: true,
                            validator: (p0) {
                              if (p0!.isEmpty) {
                                return "This field can't be empty";
                              }
                              return null;
                            },
                          ),
                          Obx(() => selectedChannel.value == "Visa"
                              ? Column(children: [
                                  Align(
                                      alignment: Alignment.centerLeft,
                                      child: customText("Email", context)),
                                  CustomTextField(
                                    controller: emailController,
                                    labelText: "Email",
                                    isRequired: true,
                                    validator: (p0) {
                                      if (p0!.isEmpty) {
                                        return "This field can't be empty";
                                      }
                                      return null;
                                    },
                                  ),
                                ])
                              : const SizedBox()),
                          ContributeChannelsBuilder(
                              selectedChannel: selectedChannel.value,
                              onChange: (String? value) {
                                selectedChannel.value = value ?? '';
                              }),
                          const Divider(),
                          Obx(() {
                            if (chamaController
                                .isGetMemberPenUsingAccNo.value) {
                              return const CircularProgressIndicator();
                            }
                            if (chamaController.hasPenalties.value) {
                              return Column(
                                children: [
                                  Text(
                                    'It looks like ${chamaController.accountNo.string} has ${chamaController.memPenaltiesUsingAcc.length} penalties.',
                                    style: context.titleMedium
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  Obx(() => Column(
                                        children: [
                                          RadioListTile<String>(
                                            title: const Text('Pay Penalty'),
                                            value: 'penalty',
                                            groupValue: contributionType.value,
                                            onChanged: (value) {
                                              contributionType.value = value;
                                              isPayingPenalty.value = true;
                                            },
                                          ),
                                          RadioListTile<String>(
                                            title: const Text('Contribute'),
                                            value: 'contribute',
                                            groupValue: contributionType.value,
                                            onChanged: (value) {
                                              contributionType.value = value;
                                              isPayingPenalty.value = false;
                                              contributeAndPayPenalties(false);
                                            },
                                          ),
                                        ],
                                      )),
                                ],
                              );
                            } else {
                              return const SizedBox.shrink();
                            }
                          }),
                          const SizedBox(
                            height: 5,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              persistentFooterButtons: [
                Obx(
                  () => FilledButton(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: SizedBox(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children:
                              chamaController.isChamaContributionLoading.isTrue
                                  ? [
                                      Padding(
                                        padding: EdgeInsets.all(2.0.r),
                                        child: const CircularProgressIndicator(
                                          color: Colors.white,
                                          strokeWidth: 2,
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Text(
                                        'Processing...',
                                        style: context.titleMedium
                                            ?.copyWith(color: Colors.white),
                                      ),
                                    ]
                                  : [
                                      Text(isPayingPenalty.value
                                          ? "Pay Penalty"
                                          : "Contribute"),
                                    ],
                        ),
                      ),
                    ),
                    onPressed: () {
                      if (contributionType.value == 'penalty') {
                        showPenaltyDialog();
                      } else {
                        contributeAndPayPenalties(false);
                      }
                    },
                  ),
                )
              ]);
        });
  }
}
