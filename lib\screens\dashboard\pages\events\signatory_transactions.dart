import 'dart:io'; 
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/events/signatory_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/signatory_approval.dart';
import 'package:onekitty/models/events/signatory_transaction_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/events_page.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/main.dart' show isLight;

import '../contribution/viewing_single_kitty/viewing_single_kitty_page.dart';

class SignatoryTransactions extends StatefulWidget {
  final int kittyId;
  const SignatoryTransactions({super.key, required this.kittyId});

  @override
  State<SignatoryTransactions> createState() => _SignatoryTransactionsState();
}

class _SignatoryTransactionsState extends State<SignatoryTransactions> {
  final controller = Get.put(SignatoryTransactionController());
  @override
  void initState() {
    controller.signatoryTransactions.clear();
    super.initState();
    _fetchData();
  }

  Future<void> _fetchData() async {
    await controller.fetchSignatoryTransactions(widget.kittyId);
  }

  Future<void> _refreshData() async {
    controller.signatoryTransactions.clear();
    return _fetchData();
  }

  final ValueNotifier<int?> _selected = ValueNotifier(null);
  final ValueNotifier<int?> _selected2 = ValueNotifier(null);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (b) {
        ViewingSingleKittyScreen.onRefresh();
      },
      child: Scaffold(
          appBar: AppBar(
            title:
                const FittedBox(child: Text('Signatory Approval Transactions')),
          ),
          body: Obx(() => controller.isFetchngSignatories.value 
              ? _buildFancyLoader() :
              controller.signatoryTransactions.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      
                      const SizedBox(height: 16),
                      Text(
                        'No transactions found',
                        style: TextStyle(
                          color: isLight.value ? AppColors.primary : Colors.white,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                )
              : DefaultTabController(
                  length: 2,
                  child: Column(
                    children: [
                      ColoredBox(
                        color: isLight.value ? AppColors.primary : Colors.transparent,
                        child: const Padding(
                          padding: EdgeInsets.only(bottom: 2.0),
                          child: TabBar(
                            labelColor: Colors.white,
                            unselectedLabelColor: Colors.grey,
                            tabs: [
                              Tab(text: 'Pending'),
                              Tab(text: 'Approved'),
                            ],
                          ),
                        ),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: TabBarView(
                            children: [
                              _buildPendingList(),
                              _buildApprovedList(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ))),
    );
  }

  Widget _buildFancyLoader() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SpinKitDualRing(
            color:  AppColors.primary  ,
            size: 50.0,
            lineWidth: 3,
          ),
          SizedBox(height: 20.h),
          Text(
            'Loading transactions...',
            style: TextStyle(
              color:   AppColors.primary ,
              fontSize: 16.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingList() {
    return ValueListenableBuilder(
      valueListenable: _selected,
      builder: (context, selected, _) {
        return Obx(() {
          if (controller.isLoading.value &&
              controller.currentPagePending.value == 0) {
            return EventShimmer(itemHeight: 100.h);
          }
          // The issue is with the logical condition in the where clause
          // Using || (OR) means it will include transactions that are not PROCESSED OR not DECLINED
          // This actually includes ALL transactions since a transaction can't be both statuses at once
          // We need to use && (AND) to get transactions that are neither PROCESSED nor DECLINED
          
          final pendingTransactions = controller.signatoryTransactions
              .where((e) => e.status != 'PROCESSED' && e.status != 'DECLINED')
              .toList();

          if (pendingTransactions.isEmpty) {
            return RefreshIndicator(
              onRefresh: _refreshData,
              child: SizedBox(
                height: 550.h,
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: const [
                    SizedBox(height: 20),
                    Center(
                      child: Text('No pending transactions'),
                    ),
                  ],
                ),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _refreshData,
            child: ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: controller.pendingScrollController,
              itemCount: pendingTransactions.length +
                  (controller.hasMorePending.value ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= pendingTransactions.length) {
                  return controller.isLoadingMore.value
                      ? _buildLoadingIndicator()
                      : const SizedBox.shrink();
                }

                final transaction = pendingTransactions[index];
                return GestureDetector(
                  onTap: () => _selected.value = index,
                  child: TransactionCard(
                    kittyId: controller.kittyId.value,
                    isApproved: false,
                    transaction: transaction,
                    title: transaction.reason,
                    amount: transaction.amount.toString(),
                    status: transaction.status,
                    isExpanded: selected == index,
                    account: transaction.receiverAccount,
                    response: transaction.response,
                    transferMode: transaction.transferMode,
                    date: DateFormat('d MMM yyyy h:mm a')
                        .format(transaction.createdAt!.toLocal()),
                    initiatedBy: transaction.initiator?.firstName,
                  ),
                );
              },
            ),
          );
        });
      },
    );
  }

  Widget _buildApprovedList() {
    return ValueListenableBuilder(
      valueListenable: _selected2,
      builder: (context, selected, _) {
        return Obx(() {
          if (controller.isLoading.value &&
              controller.currentPageApproved.value == 1) {
            return EventShimmer(itemHeight: 100.h);
          }

          final approvedTransactions = controller.signatoryTransactions
              .where((e) => e.status == 'PROCESSED' || e.status == 'DECLINED')
              .toList();

          if (approvedTransactions.isEmpty) {
            return RefreshIndicator(
              onRefresh: _refreshData,
              child: SizedBox(
                height: 550.h,
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: const [
                    SizedBox(height: 20),
                    Center(
                      child: Text('No approved transactions'),
                    ),
                  ],
                ),
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: _refreshData,
            child: ListView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              controller: controller.approvedScrollController,
              itemCount: approvedTransactions.length +
                  (controller.hasMoreApproved.value ? 1 : 0),
              itemBuilder: (context, index) {
                if (index >= approvedTransactions.length) {
                  return controller.isLoadingMore.value
                      ? _buildLoadingIndicator()
                      : const SizedBox.shrink();
                }

                final transaction = approvedTransactions[index];
                return GestureDetector(
                  onTap: () => _selected2.value = index,
                  child: TransactionCard(
                    kittyId: controller.kittyId.value,
                    isApproved: true,
                    transaction: transaction,
                    title: transaction.reason,
                    amount: transaction.amount.toString(),
                    status: transaction.status,
                    isExpanded: selected == index,
                    account: transaction.receiverAccount,
                    response: transaction.response,
                    transferMode: transaction.transferMode,
                    date: DateFormat('d MMM yyyy h:mm a')
                        .format(transaction.createdAt!.toLocal()),
                    initiatedBy: transaction.initiator?.firstName,
                  ),
                );
              },
            ),
          );
        });
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Center(
        child: SpinKitThreeBounce(
          color: isLight.value ? AppColors.primary : Colors.white,
          size: 24.0,
        ),
      ),
    );
  }
}

class TransactionCard extends StatelessWidget {
  final String title;
  final String amount;
  final String status;
  final String response;
  final bool isExpanded;
  final String? account;
  final String? transferMode;
  final String? date;
  final String? initiatedBy;
  final SignatoryTransaction transaction;
  final bool isApproved;
  final int kittyId;

  const TransactionCard({
    super.key,
    required this.title,
    required this.amount,
    required this.status,
    required this.isExpanded,
    required this.account,
    required this.transferMode,
    required this.date,
    required this.initiatedBy,
    required this.transaction,
    this.isApproved = false,
    required this.kittyId,
    required this.response,
  });

  @override
  Widget build(BuildContext context) {
    final SignatoryTransactionController controller = Get.find();
    void onActionComplete() async {
      ViewingSingleKittyScreen.onRefresh();
    }

    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListTile(
            title: Text(
              title,
            ),
            subtitle: Text(FormattedCurrency().getFormattedCurrency(amount)),
            trailing: Text(
              status,
              style: TextStyle(
                color: status == 'PROCESSING'
                    ? Colors.orange
                    : status == 'INITIALIZED'
                        ? Colors.blue
                        : status == 'PROCESSED'
                            ? Colors.green
                            : status == 'DECLINED'
                                ? Colors.red
                                : Colors.black,
              ),
            ),
          ),
          if (isExpanded) const Divider(),
          if (isExpanded)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DetailRow('ACCOUNT:', account!),
                  DetailRow('TRANSFER MODE:', transferMode!),
                  DetailRow('DATE:', date!),
                  DetailRow('AMOUNT:',
                      FormattedCurrency().getFormattedCurrency(amount)),
                  DetailRow('INITIATED BY:', initiatedBy!),
                  DetailRow('STATUS:', status),
                  if (response != "") DetailRow('RESPONSE:', response),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton.icon(
                      onPressed: () {
                        Get.to(()=>SignatoryReadMore(
                          transaction: transaction,
                          isApproved: true,
                        ));
                      },
                      label: const Text('ReadMore'),
                      icon: const Icon(Icons.launch),
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (!isApproved)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MyButton(
                          color: Colors.red,
                          onClick: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                final commentController =
                                    TextEditingController();
                                return AlertDialog(
                                    title: const Text('Enter your comment'),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        TextField(
                                          onChanged: (val) {
                                            controller.comment.value =
                                                val == "" || val.isEmpty
                                                    ? false
                                                    : true;
                                          },
                                          controller: commentController,
                                          decoration: const InputDecoration(
                                            hintText: 'Enter your comment',
                                            labelText: 'Comment',
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        Obx(
                                          () => MyButton(
                                            isGreyedOut:
                                                !controller.comment.value,
                                            showLoading:
                                                controller.isProcessing.value,
                                            onClick: () {
                                              if (commentController
                                                  .text.isNotEmpty) {
                                                controller
                                                    .processTransaction(
                                                  isApproved: false,
                                                  comment:
                                                      commentController.text,
                                                  signatoryTransaction:
                                                      transaction,
                                                )
                                                    .whenComplete(() {
                                                  controller
                                                      .fetchSignatoryTransactions(
                                                          kittyId);
                                                  onActionComplete();
                                                  Navigator.of(context).pop();
                                                });
                                              }
                                            },
                                            label: 'Submit',
                                          ),
                                        ),
                                      ],
                                    ));
                              },
                            );
                          },
                          label: 'DECLINE',
                        ),
                        MyButton(
                          onClick: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                final commentController =
                                    TextEditingController();
                                return AlertDialog(
                                    title: const Text('Enter your comment'),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        TextField(
                                          onChanged: (val) {
                                            controller.comment.value =
                                                val == "" || val.isEmpty
                                                    ? false
                                                    : true;
                                          },
                                          controller: commentController,
                                          decoration: const InputDecoration(
                                            hintText: 'Enter your comment',
                                            labelText: 'Comment',
                                          ),
                                        ),
                                        const SizedBox(height: 16),
                                        Obx(
                                          () => MyButton(
                                            isGreyedOut:
                                                !controller.comment.value,
                                            showLoading:
                                                controller.isProcessing.value,
                                            onClick: () {
                                              if (commentController
                                                  .text.isNotEmpty) {
                                                controller
                                                    .processTransaction(
                                                  isApproved: true,
                                                  comment:
                                                      commentController.text,
                                                  signatoryTransaction:
                                                      transaction,
                                                )
                                                    .whenComplete(() {
                                                  controller
                                                      .fetchSignatoryTransactions(
                                                          kittyId);
                                                  Navigator.of(context).pop();
                                                });
                                              }
                                            },
                                            label: 'Submit',
                                          ),
                                        ),
                                      ],
                                    ));
                              },
                            );
                          },
                          label: 'APPROVE',
                        ),
                      ],
                    ),
                ],
              ),
            ),
        ],
      ),
    );
  }
}

class DetailRow extends StatelessWidget {
  final String label;
  final String? value;

  const DetailRow(this.label, this.value, {super.key});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          Expanded(
            child: Text(

              value ?? '',
              textAlign: TextAlign.end,
              style: const TextStyle(

                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );

    /*Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value),
        ],
      ),
    );*/
  }
}

class SignatoryReadMore extends StatelessWidget {
  final SignatoryTransaction transaction;
  final bool isChama;
  final int? index;
  final bool isApproved;
  SignatoryReadMore(
      {super.key,
      this.index = 0,
      this.isChama = false,
      this.isApproved = false,
      required this.transaction});

  @override
  Widget build(BuildContext context) {
    if (index == null) {
      throw ('index cannot be 0');
    }
    final controller = Get.put(SignatoryTransactionController());
    return Scaffold(
      appBar: AppBar(
        title: const FittedBox(child: Text('Signatory Approval Transactions')),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: isApproved
          ? const SizedBox()
          : SizedBox(
              height: 60.h,
              width: 350.w,
              child: isChama
                  ? SizedBox(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          OutlinedButton(
                              style: OutlinedButton.styleFrom(
                                  side: BorderSide(color: Colors.red.shade900)),
                              onPressed: () {
                                showApproveDialog(index, false, context);
                              },
                              child: Text("DECLINE",
                                  style:
                                      TextStyle(color: Colors.red.shade900))),
                          ElevatedButton(
                              onPressed: () {
                                showApproveDialog(index, true, context);
                              },
                              child: const Text("APPROVE"))
                        ],
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(bottom: 16.h),
                          child: MyButton(
                            color: Colors.red,
                            onClick: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  final commentController =
                                      TextEditingController();
                                  return AlertDialog(
                                      title: const Text('Enter your comment'),
                                      content: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          TextField(
                                            onChanged: (val) {
                                              controller.comment.value =
                                                  val == "" || val.isEmpty
                                                      ? false
                                                      : true;
                                            },
                                            controller: commentController,
                                            decoration: const InputDecoration(
                                              hintText: 'Enter your comment',
                                              labelText: 'Comment',
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Obx(
                                            () => MyButton(
                                              isGreyedOut:
                                                  !controller.comment.value,
                                              showLoading:
                                                  controller.isProcessing.value,
                                              onClick: () {
                                                if (commentController
                                                    .text.isNotEmpty) {
                                                  controller
                                                      .processTransaction(
                                                        isApproved: false,
                                                        comment:
                                                            commentController
                                                                .text,
                                                        signatoryTransaction:
                                                            transaction,
                                                      )
                                                      .whenComplete(() =>
                                                          Navigator.of(context)
                                                              .pop());
                                                }
                                              },
                                              label: 'Submit',
                                            ),
                                          ),
                                        ],
                                      ));
                                },
                              );
                            },
                            label: 'DECLINE',
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(bottom: 16.h),
                          child: MyButton(
                            onClick: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  final commentController =
                                      TextEditingController();
                                  return AlertDialog(
                                      title: const Text('Enter your comment'),
                                      content: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          TextField(
                                            onChanged: (val) {
                                              controller.comment.value =
                                                  val == "" || val.isEmpty
                                                      ? false
                                                      : true;
                                            },
                                            controller: commentController,
                                            decoration: const InputDecoration(
                                              hintText: 'Enter your comment',
                                              labelText: 'Comment',
                                            ),
                                          ),
                                          const SizedBox(height: 16),
                                          Obx(
                                            () => MyButton(
                                              isGreyedOut:
                                                  !controller.comment.value,
                                              showLoading:
                                                  controller.isProcessing.value,
                                              onClick: () {
                                                if (commentController
                                                    .text.isNotEmpty) {
                                                  controller
                                                      .processTransaction(
                                                        isApproved: true,
                                                        comment:
                                                            commentController
                                                                .text,
                                                        signatoryTransaction:
                                                            transaction,
                                                      )
                                                      .whenComplete(() =>
                                                          Navigator.of(context)
                                                              .pop());
                                                }
                                              },
                                              label: 'Submit',
                                            ),
                                          ),
                                        ],
                                      ));
                                },
                              );
                            },
                            label: 'APPROVE',
                          ),
                        ),
                      ],
                    ),
            ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: ListView(
          children: [
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Text('Initiator: ',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      )),
                  Expanded(child: Divider()),
                ],
              ),
            ),
            if (kDebugMode) DetailRow('ID', "${transaction.id}"),
            DetailRow(
                'Names',
                "${transaction.initiator?.firstName ?? transaction.initator?.firstName ?? ''} "
                    "${transaction.initiator?.lastName ?? transaction.initator?.secondName ?? ''} "),
            DetailRow(
                'Phone:',
                transaction.initiator?.phoneNumber ??
                    transaction.initator?.phoneNumber ??
                    ''),
            DetailRow('Email:', transaction.initiator?.email ?? ''),
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Text('Transaction Details: ',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      )),
                  Expanded(child: Divider()),
                ],
              ),
            ),
            DetailRow('Amount',
                FormattedCurrency().getFormattedCurrency(transaction.amount)),
            if (transaction.approvalStatus != '')
              DetailRow('Approal Status:', transaction.approvalStatus),
            DetailRow(
                'Initiated At:',
                DateFormat('d MMM yyyy h:mm a')
                    .format(transaction.initiatedAt!.toLocal())),
            DetailRow(
                transaction.transferMode == "PAYBILL"
                    ? 'Account: Mpesa Paybill'
                    : transaction.transferMode == "TILL"
                        ? 'Account: Mpesa Till Number'
                        : transaction.transferMode == "BANK"
                            ? 'Bank Account Number: '
                            : 'Receiver Account',
                "${transaction.receiverAccount} ${transaction.receiverAccountRef != '' ? '-' : ''} ${transaction.receiverAccountRef}"),
            DetailRow('Reasons', transaction.reason),
            if (transaction.response != '')
              DetailRow('Response', transaction.response),
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Text('Signatories : ',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                      )),
                  Expanded(child: Divider()),
                ],
              ),
            ),
            Text(
                'Signed by ${transaction.signatures != null && transaction.signatures!.isNotEmpty ? transaction.signatures?.length : transaction.signaturesResponse?.length} of ${transaction.totalSignaturesRequired}'),
            if (transaction.signatures != null &&
                transaction.signatures!.isNotEmpty)
              ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: transaction.signatures!.length,
                  itemBuilder: (context, index) {
                    final signature = transaction.signatures![index];
                    return ListTile(
                      title: Text(
                          "${signature.delegate?.firstName ?? signature.delegatePhone} ${signature.delegate?.lastName ?? ""}"),
                      subtitle: Text(signature.comment),
                      leading: Text("${index + 1}"),
                      trailing: Text(
                        signature.isApproved ? "Approved" : "Declined",
                        style: TextStyle(
                            color: signature.isApproved
                                ? Colors.green
                                : Colors.red),
                      ),
                    );
                  }),
            if (transaction.signaturesResponse != null &&
                transaction.signaturesResponse!.isNotEmpty)
              ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: transaction.signaturesResponse!.length,
                  itemBuilder: (context, index) {
                    final signature = transaction.signaturesResponse![index];
                    return ListTile(
                      title: Text(signature.phoneNumber),
                      subtitle: Text(signature.comment),
                      leading: Text("${index + 1}"),
                      trailing:
                          Text(signature.isApproved ? "Approved" : "Declined"),
                    );
                  }),
            if (transaction.processedBy?.firstName != "")
              Column(
                children: [
                  const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Text('Processed : ',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            )),
                        Expanded(child: Divider()),
                      ],
                    ),
                  ),
                  DetailRow(
                      'Processed by:',
                      transaction.processedBy?.firstName ??
                          transaction.approvedBy?.firstName ??
                          ''),
                  DetailRow(
                      'role:',
                      transaction.processedBy?.role ??
                          transaction.approvedBy?.role),
                  DetailRow(
                      'Processed At: ',
                      (transaction.processedBy?.createdAt ??
                                  transaction.approvedBy?.createdAt) !=
                              null
                          ? DateFormat('EEE dd MMM yyyy').format(
                              transaction.processedBy?.createdAt ??
                                  transaction.approvedBy!.createdAt!)
                          : ''),
                  DetailRow(
                      'status:',
                      transaction.processedBy?.status ??
                          transaction.approvedBy?.status),
                ],
              ),
            SizedBox(height: 300.h),
          ],
        ),
      ),
    );
  }

  final commentController = TextEditingController();
  showApproveDialog(index, bool isApprove, context) async {
    final formKey = GlobalKey<FormState>();

    final chamaController = Get.find<ChamaController>();
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text(
              isApprove
                  ? "Kindly fill out the field to complete this transaction"
                  : "Confirm to decline this transaction",
              style: context.titleText,
            ),
            content: Visibility(
              visible: isApprove,
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomTextField(
                      controller: commentController,
                      labelText: "Comment",
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "This field can't be empty";
                        }
                        return null;
                      },
                    )
                  ],
                ),
              ),
            ),
            actions: [
              isApprove
                  ? const SizedBox()
                  : OutlinedButton(
                      onPressed: () async {
                        await approveTransaction(index, false, context);
                        Navigator.pop(context);
                      },
                      child: const Text("OK")),
              isApprove
                  ? Obx(() => CustomKtButton(
                      width: 120.w,
                      isLoading:
                          chamaController.isSignatoryApproveLoading.isTrue,
                      onPress: () async {
                        await approveTransaction(index, true, context);
                      },
                      btnText: "Approve"))
                  : const SizedBox()
            ],
          );
        });
  }

  approveTransaction(index, bool isApprove, context) async {
    final box = GetStorage();
    final chamaController = Get.find<ChamaController>();
    //if (formKey.currentState!.validate()) {
    final tra = chamaController.sigTransactions[index];
    final chamaDataController = Get.find<ChamaDataController>();
    String deviceId = "";
    String deviceModel = "";
    var isAuthenticated =
        await Get.to(() => AuthPasswdScreen(), arguments: [false]);
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

      deviceId = androidInfo.id;
      deviceModel = androidInfo.model;

      print('Running on ${androidInfo.id} ${androidInfo.model}');
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print(
          'Running on ${iosInfo.utsname.machine} ${iosInfo.identifierForVendor} ${iosInfo.model}');

      deviceId = iosInfo.identifierForVendor!;
      deviceModel = iosInfo.model;
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      print('Running on ${webBrowserInfo.userAgent}');

      deviceId = webBrowserInfo.userAgent!;
    }
    SignatoryApprovalModel request = SignatoryApprovalModel(
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: chamaDataController.chama.value.member?.id,
        isApproved: isApprove ? true : false,
        comment: isApprove ? commentController.text.trim() : "Decline",
        transactionId: tra.id,
        latitude: box.read(CacheKeys.lat),
        longitude: box.read(CacheKeys.long),
        deviceId: deviceId,
        deviceModel: deviceModel);
    if (isAuthenticated != null && isAuthenticated == true) {
      bool res = await chamaController.signatoryApproval(request: request);
      if (res) {
        Snack.show(res, chamaController.apiMessage.string);

        chamaController.sigTransactions.removeAt(index);
        Navigator.pop(context);
      } else {
        Snack.show(res, chamaController.apiMessage.string);
      }
    } else {
      ToastUtils.showToast("You need to authenticate to make this operation");
    }
    //}
  }
}
