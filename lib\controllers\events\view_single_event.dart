import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';

class ViewSingleEventController extends GetxController implements GetxService {
  Rx<Event> event = Event().obs;
  final transactions = <TransactionModel>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
  Future fetchTransactions(int eventId, {int? size = 20, int? page = 0}) async {
    try {
      final response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId&size=$size&page=$page",
      );
      if (response.data != null && response.data['data'] != null) {
        final returnedData = response.data['data']['items'] as List;
        transactions.value = returnedData
            .map((item) => TransactionModel.fromJson(item))
            .toList();
      } else {
        Get.snackbar('Error', response.data['message'] ?? 'Failed to load transactions',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      // Get.snackbar('Error', 'an error has occured');
      logger.e('$e');
    }
  }
}
