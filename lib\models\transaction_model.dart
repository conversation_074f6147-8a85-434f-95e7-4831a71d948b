// To parse this JSON data, do
//
//     final transac = transacFromJson(jsonString);

import 'dart:convert';

Transac transacFromJson(String str) => Transac.fromJson(json.decode(str));

String transacToJson(Transac data) => json.encode(data.toJson());

class Transac {
  bool? status;
  String? message;
  transData? data;

  Transac({
    this.status,
    this.message,
    this.data,
  });

  factory Transac.fromJson(Map<String, dynamic> json) => Transac(
        status: json["status"],
        message: json["message"],
        data: json["data"] != null ? transData.fromJson(json["data"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class transData {
  List<transItem>? items;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  transData({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory transData.fromJson(Map<String, dynamic> json) => transData(
        items: json["items"] != null
            ? List<transItem>.from(
                json["items"].map((x) => transItem.fromJson(x)))
            : null,
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

class transItem {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? merchantId;
  Merchant? merchant;
  num? amount;
  num? merchantBalance;
  String? transactionRef;
  int? kittyId;
  int? transactionStatus;

  transItem({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.merchantId,
    this.merchant,
    this.merchantBalance,
    this.amount,
    this.transactionRef,
    this.kittyId,
    this.transactionStatus,
  });

  factory transItem.fromJson(Map<String, dynamic> json) => transItem(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        merchantId: json["merchantID"],
        merchant: Merchant.fromJson(json["merchant"]),
        amount: double.tryParse(json["amount"].toString()),
        merchantBalance: double.tryParse(json["merchant_balance"].toString()),
        transactionRef: json["transaction_ref"],
        kittyId: json["kitty_id"],
        transactionStatus: json["transaction_status"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "merchantID": merchantId,
        "merchant": merchant?.toJson(),
        "amount": double.tryParse(amount.toString()),
        "merchant_balance": double.tryParse(merchantBalance.toString()),
        "transaction_ref": transactionRef,
        "kitty_id": kittyId,
        "transaction_status": transactionStatus,
      };
}

class Merchant {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? userId;
  String? merchantName;
  num? merchantCode;
  num? merchantPercent;

  Merchant({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.merchantName,
    this.merchantCode,
    this.merchantPercent,
  });

  factory Merchant.fromJson(Map<String, dynamic> json) => Merchant(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["UserID"],
        merchantName: json["merchant_name"],
        merchantCode: json["merchant_code"],
        merchantPercent: json["merchant_percent"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "UserID": userId,
        "merchant_name": merchantName,
        "merchant_code": merchantCode,
        "merchant_percent": merchantPercent,
      };
}
