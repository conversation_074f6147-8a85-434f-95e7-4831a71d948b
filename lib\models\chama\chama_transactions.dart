// To parse this JSON data, do
//
//     final chamaTransactions = chamaTransactionsFromJson(jsonString);

import 'dart:convert';

ChamaTransactions chamaTransactionsFromJson(String str) =>
    ChamaTransactions.fromJson(json.decode(str));

String chamaTransactionsToJson(ChamaTransactions data) =>
    json.encode(data.toJson());

class ChamaTransactions {
  bool? status;
  String? message;
  TransDts? data;

  ChamaTransactions({
    this.status,
    this.message,
    this.data,
  });

  factory ChamaTransactions.fromJson(Map<String, dynamic> json) =>
      ChamaTransactions(
        status: json["status"],
        message: json["message"],
        data: TransDts.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
      };
}

class TransDts {
  List<Transaction>? transactions;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  TransDts({
    this.transactions,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory TransDts.fromJson(Map<String, dynamic> json) => TransDts(
        transactions: List<Transaction>.from(
            json["items"].map((x) => Transaction.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(transactions!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

class Transaction {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? accountNumber;
  String? firstName;
  String? secondName;
  String? transactionCode;
  String? transactionCodeOther;
  String? internalId;
  String? phoneNumber;
  int? kittyId;
  String? paymentRef;
  int? memberId;
  dynamic penaltyId;
  int? userId;
  String? currencyCode;
  String? channelName;
  String? channelCode;
  num? amount;
  String? typeInOut;
  String? status;
  String? transactionType;
  String? transactionCategory;
  int? metadataId;

  Transaction({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.accountNumber,
    this.firstName,
    this.secondName,
    this.transactionCode,
    this.transactionCodeOther,
    this.internalId,
    this.phoneNumber,
    this.kittyId,
    this.paymentRef,
    this.memberId,
    this.penaltyId,
    this.userId,
    this.currencyCode,
    this.channelName,
    this.channelCode,
    this.amount,
    this.typeInOut,
    this.status,
    this.transactionType,
    this.transactionCategory,
    this.metadataId,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) => Transaction(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        accountNumber: json["account_number"],
        firstName: json["first_name"] ?? "",
        secondName: json["second_name"] ?? "",
        transactionCode: json["transaction_code"],
        transactionCodeOther: json["transaction_code_other"],
        internalId: json["internal_id"],
        phoneNumber: json["phone_number"] ?? "",
        kittyId: json["kitty_id"],
        paymentRef: json["payment_ref"],
        memberId: json["member_id"],
        penaltyId: json["penalty_id"],
        userId: json["user_id"],
        currencyCode: json["currency_code"],
        channelName: json["channel_name"],
        channelCode: json["channel_code"],
        amount: json["amount"],
        typeInOut: json["type_in_out"],
        status: json["status"],
        transactionType: json["transaction_type"],
        transactionCategory: json["transaction_category"],
        metadataId: json["metadata_id"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "account_number": accountNumber,
        "first_name": firstName,
        "second_name": secondName,
        "transaction_code": transactionCode,
        "transaction_code_other": transactionCodeOther,
        "internal_id": internalId,
        "phone_number": phoneNumber,
        "kitty_id": kittyId,
        "payment_ref": paymentRef,
        "member_id": memberId,
        "penalty_id": penaltyId,
        "user_id": userId,
        "currency_code": currencyCode,
        "channel_name": channelName,
        "channel_code": channelCode,
        "amount": amount,
        "type_in_out": typeInOut,
        "status": status,
        "transaction_type": transactionType,
        "transaction_category": transactionCategory,
        "metadata_id": metadataId,
      };
}
