import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chama_contribute_request.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/cardPayment.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/confirm_payment_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class FetchChamaDetails extends StatefulWidget {
  final String chamaId;
  const FetchChamaDetails({super.key, required this.chamaId});

  @override
  State<FetchChamaDetails> createState() => _FetchChamaDetailsState();
}

class _FetchChamaDetailsState extends State<FetchChamaDetails> {
  final chamaController = Get.put(ChamaController());
  final chamaDataController = Get.put(ChamaDataController());
  final ContributeController contributeController = Get.find();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      
      


      
      // chamaDataController.singleChamaDts.value =
      //     chamaController.chamaDetails.value;
      // await chamaController.getBenficiaries(
      //     chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
      // if (mounted) {
      //   Navigator.pushReplacement(
      //       context,
      //       MaterialPageRoute(
      //           builder: (context) => const ChamaContributePage()));
      // }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Center(
      child: Container(
          padding: const EdgeInsets.all(8),
          margin: const EdgeInsets.all(20),
          alignment: Alignment.center,
          height: 200,
          width: 200,
          decoration: BoxDecoration(
              border: Border.all(), borderRadius: BorderRadius.circular(8)),
          child: const CircularProgressIndicator()),
    ));
  }
}

class ChamaContributePage extends StatefulWidget {
  const ChamaContributePage({super.key});

  @override
  State<ChamaContributePage> createState() => _ChamaContributePageState();
}

class _ChamaContributePageState extends State<ChamaContributePage> {
  TextEditingController payerController = TextEditingController();
  TextEditingController chamaMemberController = TextEditingController();
  TextEditingController amtController = TextEditingController();
  String? selectedChannel = "M-Pesa";
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  final KittyController kittyController = Get.put(KittyController());
  final formKey = GlobalKey<FormState>();
  final box = GetStorage();
  String? myPhone;
  String? chamaMemberNo;
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  bool isPayingPenalty = false;
  int selectedPenaltyId = 0;
  String? contributionType;

  contributeAndPayPenalties(bool isPenalty,
      {int? penaltyId, int? penaltyAmt}) async {
    if (formKey.currentState!.validate()) {
      final channelCode =
          kittyController.getNetworkCode(networkTitle: selectedChannel ?? "");
      ChamaContributeRequest request = ChamaContributeRequest(
        kittyId: chamaDataController.chama.value.chama?.kittyId,
        penaltyId: isPenalty ? penaltyId : null,
        userId: chamaDataController.chama.value.member?.userId,
        amount: int.parse(amtController.text.trim()),
        phoneNumber: myPhone!.substring(1),
        accountNumber: chamaController.accountNo.value,
        latitude: box.read(CacheKeys.lat),
        longitude: box.read(CacheKeys.long),
        channelCode: channelCode,
        email: chamaDataController.chama.value.chama?.email ?? "",
        paymentType: isPenalty ? "PENALTY" : "CONTRIBUTION",
      );

      bool res = await chamaController.chamaContribute(
          request: request, isConfirm: false);
      if (res) {
        Snack.show(res, chamaController.apiMessage.string);
        showDialog(
            context: context,
            builder: (context) {
              return AlertDialog(
                title: Text(
                  chamaController.apiMessage.string.toUpperCase(),
                  style: context.titleText,
                ),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("CHAMA TITLE: ${chamaController.chamaTitle.string}"),
                    Text("MEMBER: ${chamaController.memberNames.string}"),
                    Text("ACCOUNT NO: ${chamaController.ctrAccNo.string}"),
                    if (isPenalty)
                      Text("PENALTY: ${chamaController.penaltyName.string}")
                  ],
                ),
                actions: [
                  SizedBox(
                    height: 45,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: const Text("CANCEL")),
                        Obx(() => CustomKtButton(
                            isLoading: chamaController
                                .isChamaContributionLoading.isTrue,
                            width: 120.w,
                            onPress: () async {
                              var isAuthenticated = await Get.to(
                                  () => AuthPasswdScreen(),
                                  arguments: [false]);
                              if (isAuthenticated != null &&
                                  isAuthenticated == true) {
                                var resp =
                                    await chamaController.chamaContribute(
                                        request: request, isConfirm: true);
                                if (resp) {
                                  if (channelCode == 0) {
                                    Snack.show(resp,
                                        chamaController.apiMessage.string);
                                    Navigator.pop(context);
                                    Get.off(() => const ProcessPaymentOtp(
                                        isChamaContribute: true));
                                  }
                                  if (channelCode == 63902 ||
                                      channelCode == 63903) {
                                    Snack.show(resp,
                                        chamaController.apiMessage.string);
                                    Navigator.pop(context);
                                    Get.offAndToNamed(
                                        NavRoutes.viewingSingleChama);
                                  }
                                  if (channelCode == 55) {
                                    Get.off(() => const CardPayment(
                                        isChamaContribute: true));
                                  }
                                  //chamaController.memPenaltiesUsingAcc.clear();
                                } else {
                                  if (!mounted) return;
                                  Snack.show(
                                      resp, chamaController.apiMessage.string);
                                }
                              } else {
                                ToastUtils.showInfoToast(
                                    context,
                                    "You need to be authenticated to perform this operation.",
                                    "Oops");
                              }
                            },
                            btnText: "CONFIRM"))
                      ],
                    ),
                  )
                ],
              );
            });
        //Get.offAndToNamed(NavRoutes.viewingSingleChama);
        return true;
      } else {
        Snack.show(res, chamaController.apiMessage.string);
        return false;
      }
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    chamaController.memPenaltiesUsingAcc.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20).copyWith(bottom: 5),
        child: SingleChildScrollView(
          child: Form(
            key: formKey,
            child: Column(
              children: [
                const RowAppBar(),
                Text(
                  chamaDataController.chama.value.chama?.title ?? "",
                  textAlign: TextAlign.center,
                  style: context.titleText
                      ?.copyWith(fontSize: 22, fontWeight: FontWeight.bold),
                ),
                const SizedBox(
                  height: 7,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                            "Created on: ${DateFormat("yyyy-MM-dd").format(chamaDataController.chama.value.chama?.createdAt ?? DateTime.now())}"),
                        Text(
                            "End Date: ${DateFormat("yyyy-MM-dd").format(chamaDataController.chama.value.chama?.nextOccurrence ?? DateTime.now())}"),
                      ],
                    ),
                    Text(
                      "${chamaDataController.chama.value.chama?.status}",
                      style: context.dividerTextLarge
                          ?.copyWith(color: Colors.green),
                    )
                  ],
                ),
                const Divider(),
                SingleLineRow(
                  text: "Payer phone number",
                  popup: KtStrings.payerPhone,
                ),
                InternationalPhoneNumberInput(
                  onInputChanged: (num) {
                    setState(() {
                      myPhone = num.phoneNumber!;
                    });
                  },
                  onInputValidated: (bool value) {},
                  selectorConfig: const SelectorConfig(
                    selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                    useBottomSheetSafeArea: true,
                  ),
                  ignoreBlank: false,
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                  initialValue: num,
                  textFieldController: payerController,
                  formatInput: true,
                  keyboardType: const TextInputType.numberWithOptions(
                      signed: true, decimal: true),
                  inputBorder: const OutlineInputBorder(),
                  onSaved: (PhoneNumber number) {},
                ),
                SingleLineRow(
                  text: "Chama member phone number",
                  popup: KtStrings.chamaMemberPhNo,
                ),
                InternationalPhoneNumberInput(
                  onInputChanged: (num) {
                    setState(() {
                      chamaController.accountNo.value = num.phoneNumber!;
                    });
                    // chamaController.getMemberPenaltiesUsingAcc(
                    //     accNo: chamaController.accountNo.value.substring(1),
                    //     kittyId:
                    //         chamaDataController.chama.value.chama?.kittyId ??
                    //             0);
                  },
                  onInputValidated: (bool value) {
                    if (value) {
                      chamaController.getMemberPenaltiesUsingAcc(
                          accNo: chamaController.accountNo.value.substring(1),
                          kittyId:
                              chamaDataController.chama.value.chama?.kittyId ??
                                  0);
                    }
                  },
                  selectorConfig: const SelectorConfig(
                    selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                    useBottomSheetSafeArea: true,
                  ),
                  ignoreBlank: false,
                  autoValidateMode: AutovalidateMode.onUserInteraction,
                  initialValue: num,
                  textFieldController: chamaMemberController,
                  formatInput: true,
                  keyboardType: const TextInputType.numberWithOptions(
                      signed: true, decimal: true),
                  inputBorder: const OutlineInputBorder(),
                  onSaved: (PhoneNumber number) {},
                ),
                Align(
                    alignment: Alignment.centerLeft,
                    child: customText("Amount", context)),
                CustomTextField(
                  controller: amtController,
                  labelText: "Amount",
                  isRequired: true,
                  showNoKeyboard: true,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "This field can't be empty";
                    }
                    return null;
                  },
                ),
                ContributeChannelsBuilder(
                    selectedChannel: selectedChannel ?? "",
                    onChange: (String? value) {
                      setState(() {
                        selectedChannel = value;
                      });
                    }),
                const Divider(),
                Obx(() {
                  if (chamaController.isGetMemberPenUsingAccNo.value) {
                    return const CircularProgressIndicator();
                  }
                  if (chamaController.hasPenalties.value) {
                    return Column(
                      children: [
                        Text(
                          'It looks like ${chamaController.accountNo.string} has ${chamaController.memPenaltiesUsingAcc.length} penalties.',
                          style: context.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        RadioListTile<String>(
                          title: const Text('Pay Penalty'),
                          value: 'penalty',
                          groupValue: contributionType,
                          onChanged: (value) {
                            setState(() {
                              contributionType = value;
                            });
                          },
                        ),
                        RadioListTile<String>(
                          title: const Text('Contribute'),
                          value: 'contribute',
                          groupValue: contributionType,
                          onChanged: (value) {
                            setState(() {
                              contributionType = value;
                              isPayingPenalty = false;
                              amtController
                                  .clear(); // Clear the amount when contributing
                            });
                            contributeAndPayPenalties(false);
                          },
                        ),
                        Visibility(
                            visible: contributionType == "penalty",
                            child: Container(
                              decoration: BoxDecoration(
                                color: AppColors.neutral.withOpacity(0.2),
                              ),
                              height: SizeConfig.screenHeight * 0.2,
                              child: SingleChildScrollView(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Text(
                                    //   'It looks like ${chamaController.accountNo.string} has ${chamaController.memPenaltiesUsingAcc.length} penalties.',
                                    //   style: context.titleMedium
                                    //       ?.copyWith(fontWeight: FontWeight.bold),
                                    // ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    ...chamaController.memPenaltiesUsingAcc.map(
                                      (penalty) => Container(
                                        margin:
                                            const EdgeInsets.only(bottom: 5),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            border: Border.all(
                                                color:
                                                    AppColors.blueButtonColor)),
                                        child: ListTile(
                                          onTap: () {
                                            setState(() {
                                              amtController.text =
                                                  penalty.amount.toString();
                                              isPayingPenalty = true;
                                              selectedPenaltyId = penalty.id!;
                                            });
                                          },
                                          title: Text(penalty.title.toString()),
                                          subtitle:
                                              Text(penalty.status.toString()),
                                          trailing: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Text(
                                                FormattedCurrency()
                                                    .getFormattedCurrency(
                                                        penalty.amount),
                                                style: context.dividerTextSmall
                                                    ?.copyWith(
                                                        color: Colors.green),
                                              ),
                                              Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 12,
                                                      vertical: 3),
                                                  decoration: BoxDecoration(
                                                      border: Border.all(
                                                          color: AppColors
                                                              .blueButtonColor),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              16)),
                                                  child: Text(
                                                    "Pay",
                                                    style: context
                                                        .dividerTextLarge
                                                        ?.copyWith(
                                                            color: AppColors
                                                                .blueButtonColor),
                                                  ))
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                    const Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        // CustomKtButton(
                                        //   isActive: false,
                                        //   isLoading: chamaController
                                        //       .isGetMemberPenUsingAccNo.isTrue,
                                        //   width: SizeConfig.screenWidth * 0.3,
                                        //   btnText: "Pay Penalty",
                                        //   onPress: () {
                                        //     // contributeAndPayPenalties(true,
                                        //     //     penaltyId: penaltyId);
                                        //   },
                                        // ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ))
                      ],
                    );
                  } else {
                    return const SizedBox.shrink();
                    // return CustomKtButton(
                    //     isLoading:
                    //         chamaController.isChamaContributionLoading.isTrue,
                    //     onPress: () {
                    //       contributeAndPayPenalties(false);
                    //     },
                    //     btnText: "Contribute");
                  }
                }),
                const SizedBox(
                  height: 5,
                ),
                Obx(
                  () => CustomKtButton(
                    isLoading:
                        chamaController.isChamaContributionLoading.isTrue,
                    height: 50,
                    btnText: isPayingPenalty ? "Pay Penalty" : "Contribute",
                    onPress: () {
                      isPayingPenalty
                          ? contributeAndPayPenalties(
                              true,
                              penaltyId: selectedPenaltyId,
                            )
                          : contributeAndPayPenalties(false);
                    },
                  ),
                )
              ],
            ),
          ),
        ),
      )),
    );
  }
}
