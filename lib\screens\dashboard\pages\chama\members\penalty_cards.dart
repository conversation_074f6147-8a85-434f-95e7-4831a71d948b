
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/member_penalty_request.dart';
import 'package:onekitty/models/chama/penalty_model.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/themes_colors.dart';

class PenaltiesBottomSheet extends StatefulWidget {
  final ChamaMembers? member;
  const PenaltiesBottomSheet({super.key, this.member});

  @override
  State<PenaltiesBottomSheet> createState() => _PenaltiesBottomSheetState();
}

class _PenaltiesBottomSheetState extends State<PenaltiesBottomSheet> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());

  int? selectedIndex;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          child: Column(
            children: [
              Text(
                "Pick a penalty to assign from the ones below",
                style: context.dividerTextLarge?.copyWith(
                    fontSize: 15, decoration: TextDecoration.underline),
              ),
              const SizedBox(
                height: 12,
              ),
              Expanded(
                child: GetX(
                    init: ChamaController(),
                    initState: (state) {
                      Future.delayed(Duration.zero, () async {
                        try {
                          await state.controller?.getChamaPenalties(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        } catch (e) {
                          throw e;
                        }
                      });
                    },
                    builder: (ChamaController chamaController) {
                      if (chamaController.isGetChamaPenaltyLoading.isTrue) {
                        return SizedBox(
                          height: SizeConfig.screenHeight * .33,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SpinKitDualRing(
                                  color: ColorUtil.blueColor,
                                  lineWidth: 4.sp,
                                  size: 40.0.sp,
                                ),
                                const Text(
                                  "loading..",
                                  style: TextStyle(
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      } else if (chamaController.penalties.isEmpty) {
                        return const Text("No penalties added");
                      } else if (chamaController.penalties.isNotEmpty) {
                        return SizedBox(
                          height: 300,
                          child: ListView.separated(
                              itemBuilder: (context, index) {
                                final penalty =
                                    chamaController.penalties[index];

                                return PenaltyCard(
                                  member: widget.member!,
                                  index: index,
                                  penalty: penalty,
                                  amtInitialValue: penalty.amount.toString(),
                                  reasonInitialValue: penalty.title ?? "",
                                );
                              },
                              separatorBuilder: (context, index) {
                                return const Divider();
                              },
                              itemCount: chamaController.penalties.length),
                        );
                      }
                      return const Text("No penalties added yet");
                    }),
              ),
            ],
          ),
        );
      }),
    );
  }
}

class PenaltyCard extends StatefulWidget {
  final int index;
  final PenaltyModel penalty;
  final String amtInitialValue;
  final String reasonInitialValue;
  final ChamaMembers member;

  const PenaltyCard(
      {super.key,
      required this.index,
      required this.member,
      required this.penalty,
      required this.amtInitialValue,
      required this.reasonInitialValue});

  @override
  State<PenaltyCard> createState() => _PenaltyCardState();
}

class _PenaltyCardState extends State<PenaltyCard> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  int get index => widget.index;
  PenaltyModel get penalty => widget.penalty;
  int? selectedPenaltyIndex;
  TextEditingController amountController = TextEditingController();
  TextEditingController reasonController = TextEditingController();

  void onSubmitPenalty() async {
    MemberBeingPenalized memberPenalty = MemberBeingPenalized(
      memberId: widget.member.id,
      amount: amountController.text.trim().isEmpty
          ? penalty.amount
          : int.parse(amountController.text.trim()),
      reason: reasonController.text.trim().isEmpty
          ? penalty.title
          : reasonController.text.trim(),
    );
    MultiplePenaltyRequest request2 = MultiplePenaltyRequest(
        chamaId: chamaDataController.chama.value.chama?.id,
        penaltyId: penalty.id,
        members: [memberPenalty]);
    bool res = await chamaController.penalizeMultiple(request: request2);
    if (res) {
      if (!mounted) return;
      Snack.show(res, chamaController.apiMessage.string);
      Navigator.pop(context);
    } else {
      if (!mounted) return;
      Snack.show(res, chamaController.apiMessage.string);
    }
    //}
    // else{
    //   Snack.showError(message1: "Select member");
    // }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          selectedPenaltyIndex = index;
          amountController.text = widget.amtInitialValue;
          reasonController.text = widget.reasonInitialValue;
        });
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(9),
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: AppColors.blueButtonColor.withOpacity(0.5)),
            child: Text("${index + 1}"),
          ),
          const SizedBox(
            width: 12,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  penalty.title ?? "",
                  style: context.titleText?.copyWith(
                    fontWeight: FontWeight.w800,
                  ),
                ),
                Text(penalty.description ?? ""),
                Text(
                  FormattedCurrency().getFormattedCurrency(penalty.amount),
                  style: context.dividerTextLarge?.copyWith(color: Colors.red),
                ),
                if (selectedPenaltyIndex == index)
                  Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 16.0, right: 16, left: 16),
                        child: CustomTextField(
                          //initialValue: widget.amtInitialValue,
                          isRequired: true,
                          showNoKeyboard: true,
                          labelText: "Amount",
                          controller: amountController,
                          validator: (p0) {
                            if (p0!.isEmpty) {
                              return "This field cannot be empty";
                            }
                            return null;
                          },
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            top: 16.0, right: 16, left: 16),
                        child: CustomTextField(
                          //initialValue: widget.reasonInitialValue,
                          isRequired: true,
                          labelText: "Reason",
                          controller: reasonController,
                          validator: (p0) {
                            if (p0!.isEmpty) {
                              return "Kindly give a reason";
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(
                        height: 45,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            OutlinedButton(
                                onPressed: () {
                                  setState(() {
                                    selectedPenaltyIndex = null;
                                  });
                                },
                                child: const Text("CANCEL")),
                            Obx(() => CustomKtButton(
                                width: 70,
                                isLoading:
                                    chamaController.isPenalizeMultiple.isTrue,
                                onPress: onSubmitPenalty,
                                btnText: "OK"))
                          ],
                        ),
                      )
                    ],
                  )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
