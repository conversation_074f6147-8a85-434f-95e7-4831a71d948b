// To parse this JSON data, do
//
//     final userTransactionModel = userTransactionModelFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/models/transac_kitt_model.dart';

UserTransactionModel userTransactionModelFromJson(String str) =>
    UserTransactionModel.fromJson(json.decode(str));

String userTransactionModelToJson(UserTransactionModel data) =>
    json.encode(data.toJson());

class UserTransactionModel {
  List<TransactionModel>? items;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  UserTransactionModel({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory UserTransactionModel.fromJson(Map<String, dynamic> json) =>
      UserTransactionModel(
        items: (json['items'] as List<dynamic>?)
            ?.map((itemJson) => TransactionModel.fromJson(itemJson))
            .toList(),
        page: json['page'],
        size: json['size'],
        maxPage: json['max_page'],
        totalPages: json['total_pages'],
        total: json['total'],
        last: json['last'],
        first: json['first'],
        visible: json['visible'],
      );

  Map<String, dynamic> toJson() => {
        'items': items?.map((item) => item.toJson()).toList(),
        'page': page,
        'size': size,
        'max_page': maxPage,
        'total_pages': totalPages,
        'total': total,
        'last': last,
        'first': first,
        'visible': visible,
      };
}
/*
class Item {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? accountNumber;
  String? fullName;
  String? transactionCode;
  String? transactionCodeOther;
  String? internalId;
  String? phoneNumber;
  String? currencyCode;
  int? kittyId;
  String? checkoutRequestId;
  String? channelCode;
  String? transactionDate;
  dynamic amount;
  dynamic totalCharges;
  double? orgCharges;
  String? status;
  String? transactionType;
  String? transactionCategory;
  String? product;
  dynamic metadataId;
  double? thirdPartyCharges;
  String? kittyTitle;
  String? typeInOut;

  Item({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.accountNumber,
    this.fullName,
    this.transactionCode,
    this.transactionCodeOther,
    this.internalId,
    this.phoneNumber,
    this.kittyId,
    this.checkoutRequestId,
    this.channelCode,
    this.transactionDate,
    this.amount,
    this.totalCharges,
    this.orgCharges,
    this.currencyCode,
    this.status,
    this.transactionType, 
    this.transactionCategory,
    this.product,
    this.metadataId,
    this.thirdPartyCharges,
    this.kittyTitle,
    this.typeInOut,
  });

  factory Item.fromJson(Map<String, dynamic> json) => Item(
        id: json['ID'],
        createdAt: json['CreatedAt'] != null
            ? DateTime.parse(json['CreatedAt'])
            : null,
        updatedAt: json['UpdatedAt'] != null
            ? DateTime.parse(json['UpdatedAt'])
            : null,
        deletedAt: json['DeletedAt'],
        accountNumber: json['account_number'],
        fullName: json['full_name'],
        currencyCode: json["currency_code"],
        transactionCode: json['transaction_code'],
        transactionCodeOther: json['transaction_code_other'],
        internalId: json['internal_id'],
        phoneNumber: json['phone_number'],
        kittyId: json['kitty_id'],
        checkoutRequestId: json['checkout_request_id'],
        channelCode: json['channel_code'],
        transactionDate: json['transaction_date'],
        amount: json['amount'],
        totalCharges: json['total_charges'],
        orgCharges: double.tryParse(json['org_charges']?.toString() ?? ''),
        status: json['status']?.toString(),
        transactionType: json['transaction_type']?.toString(),
        transactionCategory: json['transaction_category']?.toString(),
        product: json['product']?.toString(),
        metadataId: json['metadata_id'],
        thirdPartyCharges: json['third_party_charges'] != null 
            ? double.tryParse(json['third_party_charges'].toString())
            : null,
        kittyTitle: json['kitty_title']?.toString(),
        typeInOut: json['type_in_out']?.toString(),
      );

  Map<String, dynamic> toJson() => {
        'ID': id,
        'CreatedAt': createdAt?.toIso8601String(),
        'UpdatedAt': updatedAt?.toIso8601String(),
        'DeletedAt': deletedAt,
        'account_number': accountNumber,
        'full_name': fullName,
        'transaction_code': transactionCode,
        'transaction_code_other': transactionCodeOther,
        'internal_id': internalId,
        'phone_number': phoneNumber,
        "currency_code": currencyCode,
        'kitty_id': kittyId,
        'checkout_request_id': checkoutRequestId,
        'channel_code': channelCode,
        'transaction_date': transactionDate,
        'amount': amount,
        'total_charges': totalCharges,
        'org_charges': orgCharges,
        'status': status,
        'transaction_type': transactionType,
        'transaction_category': transactionCategory,
        'product': product,
        'metadata_id': metadataId,
        'third_party_charges': thirdPartyCharges,
        'kitty_title': kittyTitle,
        'type_in_out': typeInOut,
      };
}
*/