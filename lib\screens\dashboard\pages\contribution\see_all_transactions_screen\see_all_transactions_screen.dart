import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/transaction_item.dart';
import '../../../../../utils/utils_exports.dart';
import 'package:flutter/material.dart';

// ignore_for_file: must_be_immutable
class AllTransactionsScreen extends StatefulWidget {
  final int? kittyId;
  final int? eventId;
  const AllTransactionsScreen({super.key, this.kittyId, this.eventId});

  @override
  State<AllTransactionsScreen> createState() => _AllTransactionsScreenState();
}

class _AllTransactionsScreenState extends State<AllTransactionsScreen> {
  final KittyController controller = Get.find<KittyController>();
  TextEditingController searchController = TextEditingController();
  final DataController dataController = Get.find<DataController>();
  List<String> dropdownItemList = ["Code", "Date", "Account No"];
  String selectedFilter = "";

  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();
  bool singleTrans = false;
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  List<TransactionModel> filterbyname = [];
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  final dateformat = DateFormat('EE, dd MMMM');

  @override
  void initState() {
    super.initState();
    filterbyname = controller.transactionsKitty;
  }

  @override
  void dispose() {
    searchController.dispose();
    startDate.dispose();
    endDate.dispose();
    phoneController.dispose();
    codeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: _buildAppBar(context),
      body: Column(
        children: [
         
          
          // Filter UI Section
          if (selectedFilter == "Account No") _buildPhoneNoFilterUI(context),
          if (selectedFilter == "Code") _buildCodeFilterUI(context),
          if (selectedFilter == "Date") _buildDateFilterUI(context),
          
          // Transactions List
          Expanded(
            child: _buildMainContent(context),
          ),
          
          // Pagination
          _buildPaginationSection(context),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppBar(
      elevation: 0,
      // backgroundColor: theme.colorScheme.surface,
      // surfaceTintColor: theme.colorScheme.surface,
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: Icon(
          Icons.arrow_back_ios,
          color: theme.colorScheme.onSurface,
          size: 20.sp,
        ),
      ),
      title: Text(
        'Transactions',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
      centerTitle: true,
      bottom:  PreferredSize(preferredSize: const Size(double.infinity, 60), child:  // Search and Filter Section
          _buildSearchAndFilterSection(context),)
          ,
    
      // actions: [
      //   IconButton(
      //     onPressed: () {
      //       // Add any additional action here
      //     },
      //     icon: Icon(
      //       Icons.more_vert,
      //       color: theme.colorScheme.onSurface,
      //       size: 20.sp,
      //     ),
      //   ),
      // ],
  
    );
  }

  Widget _buildSearchAndFilterSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      
      decoration: BoxDecoration(
        // color: theme.colorScheme,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: TextField(
                controller: searchController,
                onChanged: (value) {
                  if (value.isEmpty) {
                    filterbyname = List.from(controller.transactionsKitty);
                  } else {
                    filterbyname = controller.transactionsKitty
                        .where((p0) => (p0.firstName?.toLowerCase() ?? '')
                            .contains(value.toLowerCase()))
                        .toList();
                  }
                  setState(() {});
                },
                style: TextStyle(
                  fontSize: 14.sp,
                  color: theme.colorScheme.onSurface,
                ),
                decoration: InputDecoration(
                  hintText: "Search transactions...",
                  hintStyle: TextStyle(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontSize: 14.sp,
                  ),
                  border: InputBorder.none,
                  prefixIcon: Icon(
                    Icons.search_rounded,
                    color: theme.colorScheme.onSurfaceVariant,
                    size: 20.sp,
                  ),
                  suffixIcon: searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear_rounded,
                            color: theme.colorScheme.onSurfaceVariant,
                            size: 18.sp,
                          ),
                          onPressed: () {
                            searchController.clear();
                            setState(() {
                              filterbyname = List.from(controller.transactionsKitty);
                            });
                          },
                        )
                      : null,
                  contentPadding: EdgeInsets.symmetric(
                    vertical: 12.h,
                    horizontal: 4.w,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: theme.colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: CustomDropDown(
              width: 125.w,
              hintText: "Filter",
              hintStyle: TextStyle(
                fontSize: 12.sp,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              items: dropdownItemList,
              prefix: Icon(
                Icons.filter_alt_rounded,
                size: 18.sp,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              onChanged: (value) {
                setState(() => selectedFilter = value);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: startDate.text.isNotEmpty && endDate.text.isNotEmpty ||
              phoneController.text.isNotEmpty ||
              codeController.text.isNotEmpty
          ? _buildFilteredTransactionsList(context)
          : _buildTransactionsList(context),
    );
  }

  Widget _buildPaginationSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Obx(() {
        if (controller.transactionsKitty.isNotEmpty &&
            controller.results.value!.totalPages! > 0) {
          return NumberPaginator(
            numberPages: controller.results.value!.totalPages ?? 0,
            onPageChange: (int index) async {
              await controller.getKittyContributions(
                kittyId: widget.kittyId ??
                    dataController.kitty.value.kitty?.iD ??
                    0,
                page: index,
              );
              setState(() {});
            },
          );
        } else {
          return const SizedBox.shrink();
        }
      }),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: FloatingActionButton.extended(
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        icon: Icon(
          Icons.download_rounded,
          size: 18.sp,
        ),
        label: Text(
          'Export',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder: (BuildContext context) {
              return ExportContentWidget2(
                eventId: widget.eventId,
                singleTrans: singleTrans,
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildTransactionsList(BuildContext context) {
    return GetX(
      builder: (KittyController controller) {
        if (controller.loadingTransactions.isTrue) {
          return _buildLoadingState(context);
        } else if (controller.transactionsKitty.isEmpty) {
          return _buildEmptyState(context, "You have no transactions yet");
        } else if (filterbyname.isEmpty) {
          return _buildEmptyState(context, "No transactions found");
        } else if (filterbyname.isNotEmpty) {
          return GroupedListView<TransactionModel, DateTime>(
            elements: filterbyname,
            sort: false,
            groupBy: (TransactionModel element) {
              DateTime date = element.createdAt!.toLocal();
              return DateTime(date.year, date.month, date.day);
            },
            groupHeaderBuilder: (value) {
              final date = dateformat.format(value.createdAt!.toLocal());
              return Container(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                child: Text(
                  date,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              );
            },
            itemBuilder: (_, TransactionModel item) {
              return TransactionItem(
                item: item,
                eventId: widget.eventId,
              );
            },
            separator: SizedBox(height: 8.h),
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildFilteredTransactionsList(BuildContext context) {
    return GetX(
      init: KittyController(),
      builder: (KittyController controller) {
        if (controller.loadingfiltrTransactions.isTrue) {
          return _buildLoadingState(context);
        } else if (controller.filtrtransactions.isEmpty) {
          return _buildEmptyState(context, "Could not find the transactions");
        } else if (controller.filtrtransactions.isNotEmpty) {
          return GroupedListView<TransactionModel, DateTime>(
            elements: controller.filtrtransactions,
            groupStickyHeaderBuilder: (element) => Text(
              dateformat.format(element.createdAt!.toLocal()),
            ),
            groupBy: (TransactionModel element) {
              DateTime date = element.createdAt!;
              return DateTime(date.year, date.month, date.day);
            },
            stickyHeaderBackgroundColor: Colors.transparent,
            groupHeaderBuilder: (value) {
              final date = dateformat.format(value.createdAt!.toLocal());
              return Container(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                child: Text(
                  date,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              );
            },
            itemBuilder: (_, TransactionModel item) {
              return TransactionItem(
                item: item,
                eventId: widget.eventId,
              );
            },
            separator: SizedBox(height: 8.h),
          );
        } else {
          return GroupedListView<TransactionModel, DateTime>(
            elements: controller.transactionsKitty,
            groupBy: (TransactionModel element) {
              DateTime date = element.createdAt!.toLocal();
              return DateTime(date.year, date.month, date.day);
            },
            groupHeaderBuilder: (value) {
              final date = dateformat.format(value.createdAt!.toLocal());
              return Container(
                padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                child: Text(
                  date,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              );
            },
            itemBuilder: (_, TransactionModel item) {
              return TransactionItem(item: item, eventId: widget.eventId);
            },
            separator: SizedBox(height: 8.h),
          );
        }
      },
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitDualRing(
            color: theme.colorScheme.primary,
            lineWidth: 3.sp,
            size: 32.sp,
          ),
          SizedBox(height: 16.h),
          Text(
            "Loading transactions...",
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 14.sp,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, String message) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long_outlined,
            size: 48.sp,
            color: theme.colorScheme.onSurfaceVariant.withOpacity(0.5),
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: 16.sp,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneNoFilterUI(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: CustomSearchView(
                controller: phoneController,
                hintText: "Enter phone number...",
                contentPadding: EdgeInsets.all(12.h),
                onChanged: (p0) {
                  if (p0.length == 10) {
                    String newPhoneNumber = p0.substring(1);
                    phoneController.text = newPhoneNumber;
                    _updateFilter();
                  }
                },
              ),
            ),
          ),
          SizedBox(width: 12.w),
          IconButton(
            onPressed: () {
              setState(() {
                selectedFilter = "";
                phoneController.clear();
              });
            },
            icon: Icon(
              Icons.close,
              color: theme.colorScheme.error,
              size: 20.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCodeFilterUI(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: CustomSearchView(
                controller: codeController,
                hintText: "Enter transaction code...",
                contentPadding: EdgeInsets.all(12.h),
                onChanged: (p0) {
                  _updateFilter();
                },
              ),
            ),
          ),
          SizedBox(width: 12.w),
          IconButton(
            onPressed: () {
              setState(() {
                selectedFilter = "";
                codeController.clear();
              });
            },
            icon: Icon(
              Icons.close,
              color: theme.colorScheme.error,
              size: 20.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateFilterUI(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.1),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildDateField(
              context: context,
              controller: startDate,
              label: 'Start Date',
              onTap: () => _selectDate(context, startDate),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: _buildDateField(
              context: context,
              controller: endDate,
              label: 'End Date',
              onTap: () => _selectDate(context, endDate, isEndDate: true),
            ),
          ),
          SizedBox(width: 12.w),
          IconButton(
            onPressed: () {
              setState(() {
                selectedFilter = "";
                startDate.clear();
                endDate.clear();
              });
            },
            icon: Icon(
              Icons.close,
              color: theme.colorScheme.error,
              size: 20.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: TextFormField(
        controller: controller,
        style: TextStyle(fontSize: 14.sp),
        readOnly: true,
        onTap: onTap,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 12.sp,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          border: InputBorder.none,
          suffixIcon: Icon(
            Icons.calendar_today_outlined,
            size: 16.sp,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          contentPadding: EdgeInsets.all(12.h),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, TextEditingController controller,
      {bool isEndDate = false}) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (pickedDate != null) {
      final formattedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
      controller.text = formattedDate;
      if (isEndDate) {
        _updateFilter();
      }
    }
  }

  void _updateFilter() {
    setState(() {
      _fetchFilteredTransactions();
    });
  }

  void _fetchFilteredTransactions() async {
    try {
      await Get.find<KittyController>().getKittyFiltrContributions(
        eventId: widget.eventId,
        kittyId: dataController.kitty.value.kitty?.iD ?? 0,
        startDate: startDate.text,
        endDate: endDate.text,
        phoneNumber: phoneController.text,
        code: codeController.text,
      );

      setState(() {
        Get.find<KittyController>().loadingfiltrTransactions(false);
      });
    } catch (e) {
      throw e;
    }
  }
}