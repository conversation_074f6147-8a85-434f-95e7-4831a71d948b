import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/events/media_models.dart';
import 'package:onekitty/models/events/tickets_model.dart'; 
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/crud_navigation_helper.dart';
import 'package:dio/dio.dart' as dios;

class CreateEventController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString checkoutId = ''.obs;
  RxBool isloading = false.obs;
  RxList<CategoriesModel> categories = <CategoriesModel>[].obs;
  RxBool isLoadingCategories = false.obs;
  RxBool isUploading = false.obs;
  final eventMedia = <Map<String, dynamic>>[].obs;

  RxInt eventId = 0.obs;
  final eventName = "".obs;
  final eventUsername = "".obs;
  final kittyId = 0.obs;

  Future createEvent({
    required String title,
    required String username,
    required String description,
    required String phoneNumber,
    required String email,
    required String locationTip,
    required String venue,
    required int? referralCode,
    required double lat,
    required double long,
    required int catId,
    required DateTime startDate,
    required List<Map<String, dynamic>> eventMedia,
    required DateTime endDate,
  }) async {
    if (isloading.value) {
      throw Exception('Event creation already in progress');
    }
    
    isloading(true);
    try {
      // Validate inputs before making API call
      if (title.isEmpty ||
          description.isEmpty ||
          phoneNumber.isEmpty ||
          email.isEmpty) {
        throw Exception("Required fields cannot be empty");
      }

      // Create Event model with referral code and use its toJson method
      final event = Event(
        title: title,
        username: username,
        description: description,
        phoneNumber: phoneNumber,
        email: email,
        locationTip: locationTip,
        venue: venue,
        latitude: lat,
        longitude: long,
        categoryId: catId,
        startDate: startDate,
        endDate: endDate,
        eventMedia: eventMedia.map((e) => EventMedia.fromJson(e)).toList(),
        referralCode: referralCode,
      );

      // Use the Event model's toJson method which handles UTC conversion automatically
      final payload = event.toJson();

      var res = await apiProvider
          .request(url: ApiUrls.CREATEEVENT, method: Method.POST, params: payload);

      if (res.data["status"] ?? false) {
        try {
          eventId.value = res.data["data"]["event"]["ID"] ?? 0;
          kittyId.value = res.data["data"]["event"]["kitty_id"] ?? 0;
          eventName.value = res.data["data"]["event"]["title"] ?? "";
          eventUsername.value = res.data["data"]["event"]["username"] ?? "";
          // Clear media on success
          this.eventMedia.clear();
          return eventId.value;
        } catch (parseError) {
          logger.e("Error parsing response data: $parseError");
          throw Exception("Failed to parse event data");
        }
      } else {
        final errorMsg = res.data['message'] ?? 'Could not create event';
        Get.snackbar('Error', errorMsg, backgroundColor: Colors.red);
        throw Exception(errorMsg);
      }
    } catch (e) {
      logger.e("Create event error: $e");
      apiMessage("Error creating event. Please try again.");
      // Clear stale data on failure
      this.eventMedia.clear();
      throw e;
    } finally {
      isloading(false);
    }
  }

  /// Main method that creates event and tickets (optimized with fallback)
   Future<int> createEventWithTickets({
    required List eventMedia,
    required String title,
    required String username,
    required String description,
    required String phoneNumber,
    required String email,
    required String locationTip,
    required String venue,
    required int? referralCode,
    required double lat,
    required double long,
    required int catId,
    required DateTime? startDate,
    required DateTime? endDate,
    required List<Ticket> tickets,
  }) async {
    try {
      isloading(true);

      // Create Event model and use its toJson method
      final event = Event(
        title: title,
        username: username,
        description: description,
        phoneNumber: phoneNumber,
        email: email,
        locationTip: locationTip,
        venue: venue,
        latitude: lat,
        longitude: long,
        categoryId: catId,
        startDate: startDate,
        endDate: endDate,
        eventMedia: eventMedia.map((e) => EventMedia.fromJson(e)).toList(),
        tickets:  tickets,
        referralCode : referralCode,
      );

      // Single API call to create event with tickets
      var response = await apiProvider.request(
        url: ApiUrls.CREATEEVENT,
        method: Method.POST,
        params: event.toJson(),
      );

      if(response.data['status'] ?? false){
        eventMedia.clear();
        tickets.clear();
        category.value = 0;
        
      }

      if (!(response.data["status"] ?? false)) {
        Get.snackbar(
          'Error',
          response.data['message'] ?? 'Could not create event with tickets',
          backgroundColor: Colors.red
        );
        throw Exception(response.data['message'] ?? 'Could not create event with tickets');
      }

      // Parse event data from optimized response
      int createdEventId = response.data["data"]["event"]["ID"] ?? 0;
      eventId.value = createdEventId;
      kittyId.value = response.data["data"]["event"]["kitty_id"] ?? 0;
      eventName.value = response.data["data"]["event"]["title"] ?? "";
      eventUsername.value = response.data["data"]["event"]["username"] ?? "";

      // Use CRUD Navigation Helper for consistent success handling
      if (Get.context != null) {
        CrudNavigationHelper.handleEventCrudSuccess(
          context: Get.context!,
          operation: 'create',
        );
      }

      return createdEventId;
    } catch (e) {logger.e("Create event with tickets error: $e");      
      return 0;
      
    } finally {
      isloading(false);
    }
  }
  Future<bool> addSocialMedia({
    required int eventId,
    required List media,
    required String? facebook,
    required String? tiktok,
    required String? instagram,
    required String? youtube,
    required String? twitter,
    required String? hearthis,
    required String? website,
  }) async {
    if (isloading.value) {
      logger.w('Social media operation already in progress');
      return false;
    }
    
    isloading(true);
    final List<String> failedUrls = [];
    
    try {
      // Validate event ID
      if (eventId <= 0) {
        throw Exception('Invalid event ID');
      }

      // Validate and clean URLs if provided
      final Map<String, String?> socialUrls = {
        'facebook': facebook?.trim(),
        'tiktok': tiktok?.trim(),
        'Instagram': instagram?.trim(),
        'youtube': youtube?.trim(),
        'twitter': twitter?.trim(),
        'hearthis': hearthis?.trim(),
        'website': website?.trim(),
      };

      // Remove empty URLs
      socialUrls.removeWhere((key, value) => value == null || value.isEmpty);

      // Validate remaining URLs and track failures
      for (final entry in socialUrls.entries) {
        final url = entry.value!;
        try {
          final uri = Uri.parse(url);
          if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
            failedUrls.add('${entry.key}: Invalid URL format');
          }
        } catch (e) {
          failedUrls.add('${entry.key}: Invalid URL');
        }
      }
      
      if (failedUrls.isNotEmpty) {
        logger.w('Invalid URLs detected: ${failedUrls.join(', ')}');
      }

      var res = await apiProvider
          .request(url: ApiUrls.ADDSOCIALMEDIA, method: Method.POST, params: {
        "event_id": eventId,
        "media": media,
        "SocialAccounts": socialUrls,
      });

      if (res.data["status"] == true) {
        logger.i("Social media added successfully for event $eventId");
        return true;
      }
      
      final errorMessage = res.data["message"] ?? "Failed to add social media";
      logger.e("Social media API error: $errorMessage");
      throw Exception(errorMessage);
    } catch (e) {
      logger.e("Social media error: $e");
      return false;
    } finally {
      isloading(false);
    }
  }

  final  RxList<Map<String, dynamic>> bannerList = <Map<String, dynamic>>[].obs;
  final RxInt category = 0.obs;
  final  Rx<CategoriesModel?>? selCategory = Rx<CategoriesModel?>(null);

  Future<String?> uploadFile(
      {required String path, required String fileName}) async {
    if (isUploading.value) {
      logger.w('Upload already in progress');
      return null;
    }
    
    isUploading(true);
    try {
      // Check file size (limit to 10MB)
      final file = File(path);
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        throw Exception('File too large. Maximum size is 10MB');
      }
      
      var data = dios.FormData.fromMap({
        'file': await dios.MultipartFile.fromFile(path, filename: fileName),
        'bucket': 'onekitty'
      });
      
      final response = await apiProvider.request(
        url: "${HttpService.baseUrl!}${ApiUrls.UPLOADFILE}",
        method: Method.POST,
        formdata: data,
      );
      
      if (response.data['status'] ?? false) {
        return response.data['data']['file_path'];
      } else {
        final errorMsg = response.data['message'] ?? 'Upload failed';
        throw Exception(errorMsg);
      }
    } catch (e) {
      logger.e('Error uploading file: $e');
      throw Exception('Upload failed: ${e.toString()}');
    } finally {
      isUploading(false);
    }
  }

  Future<void> getCategories() async {
    try {
      categories.clear();
      categories([]);
      isLoadingCategories(true);
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.GETCATEGORIES);
      if (response.data != null) {
        final _returneddata = response.data['data']['categories'] as List;
        categories = _returneddata
            .map((item) {
              return CategoriesModel.fromJson(item);
            })
            .toList()
            .obs;
      } else {
        logger.v('No data or unexpected response structure');
      }
      isLoadingCategories(false);
    } catch (e) {
      logger.e('Error fetching events: $e');
      isLoadingCategories(false);
    }
  }

  //tickets controller
  RxList<Ticket> tickets = <Ticket>[].obs;
  var slotType = 1.obs;
}
