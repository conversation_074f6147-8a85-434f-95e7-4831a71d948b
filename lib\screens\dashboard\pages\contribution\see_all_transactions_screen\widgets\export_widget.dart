// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart'; 
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/events/view_single_event.dart'; 
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/excel/excel_func.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/statement_widget.dart';
import 'package:onekitty/services/share_whatsapp_service.dart' show ShareWhatsapp;
import 'package:onekitty/services/whatsapp_statement_export_service.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';

class ExportContentWidget extends StatelessWidget {
  TransactionModel? transaction;
  bool singleTrans;

  ExportContentWidget({
    super.key,
    this.transaction,
    required this.singleTrans,
  });
  UserKittyController userController = Get.put(UserKittyController());

  final KittyController kittyController = Get.put(KittyController());

 @override
Widget build(BuildContext context) {
  final DateFormat format = DateFormat.MMMEd().add_jms();
  DateTime createdAt = transaction?.createdAt?.toLocal() ?? DateTime.now();
  
  return Container(
    width: double.maxFinite,
    constraints: BoxConstraints(
      maxHeight: MediaQuery.of(context).size.height * 0.85,
    ),
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.surface,
      borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Handle bar
        Container(
          width: 40.w,
          height: 4.h,
          margin: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
            borderRadius: BorderRadius.circular(2.r),
          ),
        ),
        
        // Header with close button
        Padding(
          padding: EdgeInsets.fromLTRB(24.w, 8.h, 16.w, 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Export Options",
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(20.r),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: Icon(
                    Icons.close,
                    size: 20.w,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Scrollable content
        Flexible(
          child: SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 32.h),
            child: Column(
              children: [
                // Export to PDF
                _buildExportOption(
                  context: context,
                  icon: Icons.picture_as_pdf_outlined,
                  title: "Export to PDF",
                  subtitle: "Download as PDF document",
                  onTap: () {
                    if (singleTrans) {
                      Get.to(() => SingleStatementPage(
                            isContributions: false,
                            userTransactions: transaction,
                          ));
                    } else {
                      Get.to(() => UserStatementPage(
                            isContributions: true,
                            userTransactions: userController.alltransactions,
                          ));
                    }
                  },
                ),

                // Export to Excel (only for multiple transactions)
                if (!singleTrans) ...[
                  SizedBox(height: 16.h),
                  _buildExportOption(
                    context: context,
                    icon: Icons.table_chart_outlined,
                    title: "Export to Excel",
                    subtitle: "Download as spreadsheet file",
                    onTap: () => _handleExcelExport(context),
                  ),
                ],

                SizedBox(height: 16.h),

                // Export to Text
                _buildExportOption(
                  context: context,
                  icon: Icons.text_snippet_outlined,
                  title: "Export to Text",
                  subtitle: "Share as plain text message",
                  onTap: () async {
                    String shareMsg = _buildTransactionMessage(format, createdAt);
                    await Share.share(shareMsg, subject: 'Transaction details');
                  },
                ),

                // Generate Statement (only for multiple transactions)
                if (!singleTrans) ...[
                  SizedBox(height: 16.h),
                  _buildExportOption(
                    context: context,
                    icon: Icons.receipt_long_outlined,
                    title: "Generate Statement",
                    subtitle: "Create detailed financial statement",
                    onTap: () async {
                      final whatsappService = Get.put(WhatsAppStatementExportService());
                      final kittyId = transaction?.kittyId ?? userController.alltransactions.first.kittyId;
                      final kittyTitle = transaction?.kittyTitle ?? userController.alltransactions.first.kittyTitle;
                      
                      if (kittyId != null) {
                        await whatsappService.showWhatsAppStatementDialog(
                          context: context,
                          kittyId: kittyId,
                          kittyTitle: kittyTitle,
                        );
                      }
                    },
                  ),
                ],
                
                // Add some bottom spacing for scroll
                SizedBox(height: 16.h),
              ],
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _buildExportOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required String subtitle,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
          ],
        ),
      ),
    ),
  );
}

String _buildTransactionMessage(DateFormat format, DateTime createdAt) {
  return "Phone Number: ${userController.alltransactions.first.phoneNumber ?? "Anonymous"}\n"
         "Amount: KSH ${userController.alltransactions.first.amount}\n"
         "Transaction Code: ${userController.alltransactions.first.transactionCode}\n"
         "Status: ${userController.alltransactions.first.status}\n"
         "Date: ${format.format(createdAt.toLocal())}";
}

void _handleExcelExport(BuildContext context) async {
  // Show loading dialog
  _showLoadingDialog(context, 'Generating Excel file...');
  
  try {
    String filePath = await userExcel();
    Navigator.pop(context); // Close loading dialog
    _showFileOptionsBottomSheet(context, filePath);
  } catch (e) {
    Navigator.pop(context); // Close loading dialog
    _showErrorDialog(context, 'Failed to generate Excel file: $e');
  }
}

void _showLoadingDialog(BuildContext context, String message) {
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 32.w,
                height: 32.w,
                child: CircularProgressIndicator(
                  strokeWidth: 3.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(height: 16.h),
              Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    },
  );
}

void _showErrorDialog(BuildContext context, String message) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        title: Row(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 24.w,
            ),
            SizedBox(width: 8.w),
            Text(
              'Error',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: const Text('OK'),
          ),
        ],
      );
    },
  );
}

void _showFileOptionsBottomSheet(BuildContext context, String filePath) {
  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    builder: (BuildContext context) {
      return Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(bottom: 20.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Text(
              "File Options",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 24.h),
            _buildFileOption(
              context: context,
              icon: Icons.file_open,
              title: "Open File",
              onTap: () {
                Navigator.pop(context);
                OpenFile.open(filePath);
              },
            ),
            SizedBox(height: 12.h),
            _buildFileOption(
              context: context,
              icon: Icons.share,
              title: "Share File",
              onTap: () {
                Navigator.pop(context);
                Share.shareXFiles([XFile(filePath)]);
              },
            ),
            SizedBox(height: 16.h),
          ],
        ),
      );
    },
  );
}

Widget _buildFileOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
          ],
        ),
      ),
    ),
  );
}
}

class ExportContentWidget2 extends StatefulWidget {
  final TransactionModel? details;
  final Kitty? kitty;
  bool singleTrans;
  final int? eventId;

  ExportContentWidget2({
    super.key,
    this.details,
    this.kitty,
    required this.singleTrans,
    this.eventId,
  });

  @override
  State<ExportContentWidget2> createState() => _ExportContentWidget2State();
}

class _ExportContentWidget2State extends State<ExportContentWidget2> {
  KittyController kittyController = Get.put(KittyController());
  final DataController dataController = Get.put(DataController());

  String shareMsg = "";

  Future<void> fetchMessage() async {
    final res = await kittyController.shareKittyTrans(
        id: (dataController.kitty.value.kitty?.iD ?? 0));
    if (res) {
      if (mounted) {
        setState(() {
          shareMsg = kittyController.textmessage.toString();
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    fetchMessage();
  }
  // final DateFormat format = DateFormat.MMMEd().add_jms();

@override
Widget build(BuildContext context) {
  final event = widget.eventId != null
      ? Get.find<ViewSingleEventController>().event.value
      : null;
  final DateFormat format = DateFormat.MMMEd().add_jms();
  DateTime createdAt = widget.details?.createdAt ?? DateTime.now();
  
  return Container(
    width: double.maxFinite,
    constraints: BoxConstraints(
      maxHeight: MediaQuery.of(context).size.height * 0.8,
    ),
    decoration: BoxDecoration(
      color: Theme.of(context).colorScheme.surface,
      borderRadius: BorderRadius.vertical(top: Radius.circular(24.r)),
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Handle bar
        Container(
          width: 40.w,
          height: 4.h,
          margin: EdgeInsets.symmetric(vertical: 12.h),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
            borderRadius: BorderRadius.circular(2.r),
          ),
        ),
        
        // Header with close button
        Padding(
          padding: EdgeInsets.fromLTRB(24.w, 8.h, 16.w, 16.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Export Options",
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (event != null) ...[
                    SizedBox(height: 4.h),
                    Text(
                      "Event: ${event.title}",
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ] else ...[
                    SizedBox(height: 4.h),
                    Text(
                      "Kitty: ${dataController.kitty.value.kitty?.title ?? ''}",
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
              InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(20.r),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  child: Icon(
                    Icons.close,
                    size: 20.w,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Scrollable export options
        Flexible(
          child: SingleChildScrollView(
            padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 32.h),
            child: Column(
              children: [
                   // Generate Statement (only for multiple transactions)
                if (!widget.singleTrans) ...[
                  SizedBox(height: 16.h),
                  _buildExportOption(
                    context: context,
                    icon: Icons.receipt_long_outlined,
                    title: "Generate Statement",
                    subtitle: "Create detailed financial statement",
                    onTap: () async {
                      final whatsappService = Get.put(WhatsAppStatementExportService());
                      final kittyId = dataController.kitty.value.kitty?.iD
                      ;
                      final kittyTitle = dataController.kitty.value.kitty?.title;
                      
                      if (kittyId != null) {
                        await whatsappService.showWhatsAppStatementDialog(
                          context: context,
                          kittyId: kittyId,
                          kittyTitle: kittyTitle,
                        );
                      }
                    },
                  ),
                ],
                SizedBox(height: 16.h),
                // Export to PDF
                _buildExportOption(
                  context: context,
                  icon: Icons.picture_as_pdf_outlined,
                  title: "Export to PDF",
                  subtitle: "Download as PDF document",
                  onTap: () {
                    if (widget.singleTrans) {
                      Get.to(() => SingleStatementPage(
                            eventId: widget.eventId,
                            isContributions: true,
                            kitty: widget.kitty,
                            kittyTransaction: widget.details,
                          ));
                    } else {
                      Get.to(() => StatementPage(
                            eventId: widget.eventId,
                            isContributions: true,
                            kitty: widget.kitty,
                            transactions: kittyController.transactionsKitty,
                          ));
                    }
                  },
                ),

                // Export to Excel (only for multiple transactions)
                if (!widget.singleTrans) ...[
                  SizedBox(height: 16.h),
                  _buildExportOption(
                    context: context,
                    icon: Icons.table_chart_outlined,
                    title: "Export to Excel",
                    subtitle: "Download as spreadsheet file",
                    onTap: () => _handleExcelExport(context),
                  ),
                ],

                SizedBox(height: 16.h),

                // Export to Text
                _buildExportOption(
                  context: context,
                  icon: Icons.text_snippet_outlined,
                  title: "Export to Text",
                  subtitle: "Share as plain text message",
                  onTap: () async {
                    if (widget.singleTrans) {
                      String shareMsg = _buildSingleTransactionMessage(event, format, createdAt);
                      await Share.share(shareMsg, subject: 'Transaction details');
                    } else {
                      try {
                        await Share.share("${kittyController.textmessage}");
                      } catch (e) {
                        print('Error sharing: $e');
                      }
                    }
                  },
                ),

                SizedBox(height: 16.h),

                // WhatsApp Message
                _buildExportOption(
                  context: context,
                  icon: Icons.chat_outlined,
                  title: "WhatsApp Message",
                  subtitle: "Share directly via WhatsApp",
                  onTap: () async {
                    Get.back();
                    if (widget.singleTrans) {
                      String shareMsg = _buildSingleTransactionWhatsAppMessage(event, format, createdAt);
                      ShareWhatsapp.share(shareMsg);
                    } else {
                      try {
                        ShareWhatsapp.share("${kittyController.textmessage}");
                      } catch (e) {
                        print('Error sharing: $e');
                      }
                    }
                  },
                ),

             
              ],
            ),
          ),
        ),
      ],
    ),
  );
}

Widget _buildExportOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required String subtitle,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
          ],
        ),
      ),
    ),
  );
}

Future<void> _handleExcelExport(BuildContext context) async {
  // Show loading dialog
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext context) {
      return Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Container(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 32.w,
                height: 32.w,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              SizedBox(height: 20.h),
              Text(
                'Generating Excel file...',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              SizedBox(height: 8.h),
              Text(
                'This may take a moment',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );

  try {
    // Generate Excel file
    String filePath = await createExcel(isKitty: true, eventId: widget.eventId);
    
    // Close loading dialog
    Navigator.pop(context);
    
    // Show file options
    _showFileOptionsBottomSheet(context, filePath);
    
  } catch (e) {
    // Close loading dialog
    Navigator.pop(context);
    
    // Show error dialog
    _showErrorDialog(context, e.toString());
  }
}

void _showFileOptionsBottomSheet(BuildContext context, String filePath) {
  showModalBottomSheet(
    context: context,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    builder: (BuildContext context) {
      return Container(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(bottom: 20.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.2),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            Text(
              "File Options",
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 24.h),
            _buildFileOption(
              context: context,
              icon: Icons.file_open,
              title: "Open File",
              subtitle: "Open with default app",
              onTap: () {
                Navigator.pop(context);
                OpenFile.open(filePath);
              },
            ),
            SizedBox(height: 12.h),
            _buildFileOption(
              context: context,
              icon: Icons.share,
              title: "Share File",
              subtitle: "Share with other apps",
              onTap: () {
                Navigator.pop(context);
                Share.shareXFiles([XFile(filePath)]);
              },
            ),
            SizedBox(height: 16.h),
          ],
        ),
      );
    },
  );
}

Widget _buildFileOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required String subtitle,
  required VoidCallback onTap,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12.r),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                size: 20.w,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            ),
          ],
        ),
      ),
    ),
  );
}

void _showErrorDialog(BuildContext context, String error) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
        icon: Icon(
          Icons.error_outline,
          color: Theme.of(context).colorScheme.error,
          size: 32.w,
        ),
        title: Text(
          'Error',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Failed to generate Excel file: $error',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: const Text('OK'),
          ),
        ],
      );
    },
  );
}

String _buildSingleTransactionMessage(dynamic event, DateFormat format, DateTime createdAt) {
  return "Title: ${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}\n"
         "Phone Number: ${widget.details?.phoneNumber}\n"
         "Amount: KSH ${widget.details?.amount}\n"
         "Name: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\n"
         "Transaction Code: ${widget.details?.transactionCode ?? ''}\n"
         "Date: ${format.format(createdAt.toLocal())}\n"
         "${event != null ? 'Event: https://onekitty.co.ke/event/${event.username}' : 'Kitty: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD}'}";
}

String _buildSingleTransactionWhatsAppMessage(dynamic event, DateFormat format, DateTime createdAt) {
  return "${event != null ? 'Event' : 'Kitty Title'}: ${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}\n"
         "Phone Number: ${widget.details?.phoneNumber}\n"
         "Amount: KSH ${widget.details?.amount}\n"
         "Name: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\n"
         "Transaction Code: ${widget.details?.transactionCode ?? ''}\n"
         "Date: ${format.format(createdAt.toLocal())}\n"
         "${event != null ? 'Event: https://onekitty.co.ke/events/${event.username}' : 'Kitty: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.iD}'} ";
}

}
