// To parse this JSON data, do
//
//     final userKitties = userKittiesFromJson(jsonString);

import 'dart:convert';
import 'package:onekitty/models/kitty/kitt_model.dart';
import 'package:onekitty/models/kitty_model.dart';

UserKitties userKittiesFromJson(String str) =>
    UserKitties.fromJson(json.decode(str));

String userKittiesToJson(UserKitties data) => json.encode(data.toJson());

class UserKitties {
  bool? status;
  String? message;
  Data? data;

  UserKitties({
    this.status,
    this.message,
    this.data,
  });

  factory UserKitties.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserKitties(); // Return a default object if json is null
    }

    return UserKitties(
      status: json["status"],
      message: json["message"],
      data: json["data"] != null ? Data.fromJson(json["data"]) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  List<UserKitty> userKitties;

  Data({
    required this.userKitties,
  });

  factory Data.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Data(userKitties: []);

    return Data(
      userKitties: (json["user_kitties"] as List<dynamic>? ?? [])
          .map((e) => UserKitty.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        "user_kitties": List<dynamic>.from(userKitties.map((x) => x.toJson())),
      };
}

class UserKitty {
  Kitty? kitty;
  String? kittyStatus;
  String? kittBeneficiaryChannel;
  String? kittyType;
  bool? hasMembership;
  String? paymentRefLabel;
  bool? hasSignatories;
  final double? percentage;
  UserKitty(
      {this.kitty,
      this.kittyStatus,
      this.kittBeneficiaryChannel,
      this.kittyType,
      this.percentage,
      this.hasSignatories,
      this.hasMembership, this.paymentRefLabel});

  factory UserKitty.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserKitty(
        kitty: Kitty(),
        kittyStatus: '',
        kittBeneficiaryChannel: '',
        kittyType: '',
        percentage: 0.0,
        hasMembership: false,
        hasSignatories: false,
        paymentRefLabel: 'Payment Ref'
      );
    }

    return UserKitty(
      hasSignatories: json['has_signatory_transaction'] ?? false,
      kitty: json["kitty"] != null ? Kitty.fromJson(json["kitty"]) : Kitty(),
      kittyStatus: json["kitty_status"] ?? '',
      kittBeneficiaryChannel: json["kitt_beneficiary_channel"] ?? '',
      kittyType: json["kitty_type"] ?? '',
      percentage: json['percentage'],
      hasMembership: json['has_membership'] ?? false,
      paymentRefLabel: json['payment_ref_label'] ?? 'Payment Ref'
    );
  }

  Map<String, dynamic> toJson() => {
        "kitty": kitty?.toJson(),
        "has_signatory_transaction": hasSignatories,
        "kitty_status": kittyStatus,
        "kitt_beneficiary_channel": kittBeneficiaryChannel,
        "kitty_type": kittyType,
        "percentage" : percentage,
        "has_membership" : hasMembership,
        "payment_ref_label" : paymentRefLabel
      };
}