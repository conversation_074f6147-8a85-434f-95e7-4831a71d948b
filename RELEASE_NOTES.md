# Release Notes - OneKitty Mobile v2

## 🎉 Issues Fixed

### Critical Fixes
- **PDF Export Null Value Fix** ✅
  - Fixed null check operator crashes in PDF statement generation
  - Added comprehensive null safety checks across all PDF functions
  - Implemented fallback values for null transaction properties
  - Fixed logical errors in email condition validation
  - Updated both contribution and chama PDF statement files

## 🚀 Upcoming Improvements

### High Priority
- **WhatsApp URL Regex Update**
  - Updating regex pattern to handle various WhatsApp link formats
  - Maintaining backward compatibility with existing links
  - Affects kitty, chama, and event WhatsApp integrations

- **KYC Selfie Upload Process**
  - Debugging and fixing selfie upload functionality
  - Improving API integration and error handling
  - Enhanced user feedback and device compatibility

### New Features
- **WhatsApp Transaction Statements**
  - Request event transaction statements via WhatsApp
  - Transaction summary and detailed report options

- **Event Statistics Dashboard**
  - Visualization dashboard for event organizers
  - Ticket sales, revenue, and attendance metrics
  - Sales trends tracking

### UI/UX Improvements
- **Text Field Styling**
  - Implementing faint grey helper/hint text across all text fields
  - Updating themes and custom text field components

- **Permission Management**
  - Moving contacts permission request to point of use
  - Improved user experience for permission handling

- **Navigation Enhancement**
  - Auto-close pages after creating/editing events, groups, or kitties
  - Clear form data and return to appropriate screens

- **Kitties Filter System**
  - Adding filter icon button with date, sorting, and status options
  - Backend integration with query parameters for search and filtering

### Technical Improvements
- **Media Upload Validation**
  - Upload progress tracking
  - Submit button state management
  - Enhanced error handling for failed uploads
  - URL validation for media fields

---

*For technical details and implementation status, refer to issues.md and new_issues.md*