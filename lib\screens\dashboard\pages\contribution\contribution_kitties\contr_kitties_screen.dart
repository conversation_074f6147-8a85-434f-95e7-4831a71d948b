import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_or_contri_kitty_screen.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../../../controllers/user_ktty_controller.dart';
import '../../../../../utils/utils_exports.dart';
import 'widgets/single_kitty_card.dart';

// ignore_for_file: must_be_immutable
class MyKittiesScreen extends StatefulWidget {
  const MyKittiesScreen({super.key});

  @override
  State<MyKittiesScreen> createState() => _MyKittiesScreenState();
}

class _MyKittiesScreenState extends State<MyKittiesScreen> {
  DateTime? startDate, endDate;
  TextEditingController searchController = TextEditingController();

  UserModelLatest user = UserModelLatest();

  UserKittyController userController = Get.find<UserKittyController>();

  String greeting = getGreeting();

  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));
  ContributeController singleKitty = Get.put(ContributeController());

  // Debounce for search to avoid too many API calls
  Timer? _debounce;

  // Track if initial data has been loaded
  bool initialLoadDone = false;

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  // Filter state
  Map<String, String> currentFilters = {};

  void _onRefresh() async {
    searchController.clear();
    // Reset pagination and clear list before fetching page 0
    userController.reset();

    // Apply current filters if any, otherwise fetch normally
    if (currentFilters.isNotEmpty) {
      await userController.getUserkittiesWithFilters(filters: currentFilters, page: 0);
    } else {
      await userController.getUserkitties(page: 0);
    }
    _refreshController.refreshCompleted();
  }

void _showFilterDialog() {
  final searchController = TextEditingController(text: currentFilters['search'] ?? '');
  final startDateController = TextEditingController(text: currentFilters['start_date'] ?? '');
  final endDateController = TextEditingController(text: currentFilters['end_date'] ?? '');

  
  String selectedStatus = currentFilters['status'] ?? '';

  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 420.w,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.fromLTRB(24.w, 24.h, 16.w, 16.h),
              child: Row(
                children: [
                  Container(
                    width: 40.w,
                    height: 40.w,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      Icons.filter_list,
                      size: 20.w,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Filter Kitties',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          'Refine your search results',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    borderRadius: BorderRadius.circular(20.r),
                    child: Container(
                      padding: EdgeInsets.all(8.w),
                      child: Icon(
                        Icons.close,
                        size: 20.w,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            Divider(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              thickness: 1,
              height: 1,
            ),
            
            // Form content
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(24.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Search/ID field
                    _buildEnhancedTextField(
                      controller: searchController,
                      label: 'Search by Name or ID',
                      hint: 'Enter kitty name or ID',
                      icon: Icons.search,
                      context: context,
                    ),
                    
                    SizedBox(height: 20.h),
                    
                    // Status dropdown
                    _buildEnhancedStatusDropdown(
                      selectedStatus: selectedStatus,
                      onChanged: (value) => selectedStatus = value ?? '',
                      context: context,
                    ),
                    
                    SizedBox(height: 20.h),
                    
                    // Date range section
                    Text(
                      'Date Range',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                      ),
                    ),
                    SizedBox(height: 12.h),
                    Row(
                      children: [
                        Expanded(
                          child: _buildEnhancedDateField(
                            type: "startDate",
                            controller: startDateController,
                            label: 'Start Date',
                            icon: Icons.calendar_today,
                            context: context,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Expanded(

                          child: _buildEnhancedDateField(
                            type: "endDate",
                            controller: endDateController,
                            label: 'End Date',
                            icon: Icons.event,
                            context: context,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Action buttons
            Container(
              padding: EdgeInsets.fromLTRB(24.w, 16.h, 24.w, 24.h),
              child: Row(
                children: [
                  // Clear filters button
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _applyFilters({});
                      },
                      icon: Icon(Icons.clear_all, size: 18.w),
                      label: const Text('Clear All'),
                      style: OutlinedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        side: BorderSide(
                          color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                        ),
                      ),
                    ),
                  ),
                  
                  SizedBox(width: 16.w),
                  
                  // Apply filters button
                  Expanded(
                    flex: 2,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        final filters = <String, String>{};
                        
                        // Handle search/ID field
                        if (searchController.text.isNotEmpty) {
                          final searchText = searchController.text.trim();
                          // Check if it's a number (ID) or text (name)
                          if (int.tryParse(searchText) != null) {
                            filters['id'] = searchText;
                          } else {
                            filters['search'] = searchText;
                          }
                        }
                        
                        if (startDateController.text.isNotEmpty) {
                          filters['start_date'] = startDate?.toUtc().toIso8601String()??'';
                        }
                        if (endDateController.text.isNotEmpty) {
                          filters['end_date'] = endDate?.toUtc().toIso8601String()??'';
                        }
                        if (selectedStatus.isNotEmpty) {
                          final filter_id = selectedStatus.toLowerCase() == 'complete' ? 1 : selectedStatus.toLowerCase() == 'active' ? 0 : 2;
                          filters['status'] = filter_id.toString();
                        }
                        
                        Navigator.pop(context);
                        _applyFilters(filters);
                      },
                      icon: Icon(Icons.check, size: 18.w),
                      label: const Text('Apply Filters'),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 14.h),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

Widget _buildEnhancedTextField({
  required TextEditingController controller,
  required String label,
  required String hint,
  required IconData icon,
  required BuildContext context,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        label,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
        ),
      ),
      SizedBox(height: 8.h),
      TextFormField(
        controller: controller,
        decoration: InputDecoration(
          hintText: hint,
          prefixIcon: Icon(
            icon,
            size: 20.w,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
      ),
    ],
  );
}

Widget _buildEnhancedDateField({
  required String type,
  required TextEditingController controller,
  required String label,
  required IconData icon,
  required BuildContext context,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        label,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
        ),
      ),
      SizedBox(height: 8.h),
      TextFormField(
        controller: controller,
        readOnly: true,
        onTap: () async {
          final date = await showDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(2020),
            lastDate: DateTime.now().add(const Duration(days: 365)),
            builder: (context, child) {
              return Theme(
                data: Theme.of(context).copyWith(
                  colorScheme: Theme.of(context).colorScheme,
                ),
                child: child!,
              );
            },
          );
          if (date != null) {
            if(type == 'endDate'){
              endDate = date;
            }else{
              startDate = date;
            }
          
            controller.text = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
          }
        },
        decoration: InputDecoration(
          hintText: 'Select date',
          prefixIcon: Icon(
            icon,
            size: 20.w,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          suffixIcon: controller.text.isNotEmpty
              ? InkWell(
                  onTap: () => controller.clear(),
                  child: Icon(
                    Icons.clear,
                    size: 18.w,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                  ),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
        ),
      ),
    ],
  );
}

Widget _buildEnhancedStatusDropdown({
  required String selectedStatus,
  required ValueChanged<String?> onChanged,
  required BuildContext context,
}) {
  final statuses = ['Active', 'Complete', 'Draft'];
  
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        'Status',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
        ),
      ),
      SizedBox(height: 8.h),
      DropdownButtonFormField<String>(
        value: selectedStatus.isEmpty ? null : selectedStatus,
        onChanged: onChanged,
        decoration: InputDecoration(
          hintText: 'Select status',
          prefixIcon: Icon(
            Icons.info_outline,
            size: 20.w,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary,
              width: 2,
            ),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
          contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        ),
        items: [
          DropdownMenuItem<String>(
            value: '',
            child: Text(
              'All Statuses',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ),
          ...statuses.map((status) => DropdownMenuItem<String>(
            value: status,
            child: Row(
              children: [
                Container(
                  width: 8.w,
                  height: 8.w,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status, context),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                SizedBox(width: 8.w),
                Text(status),
              ],
            ),
          )),
        ],
      ),
    ],
  );
}

Color _getStatusColor(String status, BuildContext context) {
  switch (status.toLowerCase()) {
    case 'active':
      return Theme.of(context).colorScheme.primary;
    case 'complete':
      return Colors.green;
    case 'draft':
      return Colors.orange;
    default:
      return Theme.of(context).colorScheme.outline;
  }
}


  void _applyFilters(Map<String, String> filters) {
    setState(() {
      currentFilters = filters;
    });

    // Reset pagination and apply filters
    userController.reset();
    userController.getUserkittiesWithFilters(filters: filters, page: 0);
  }

  void _onLoading() async {
    // Load more data with current filters if any
    if (currentFilters.isNotEmpty) {
      await userController.getUserkittiesWithFilters(
        filters: currentFilters,
        page: userController.currentPage.value + 1,
      );
    } else {
      await userController.loadMoreKitties();
    }
    _refreshController.loadComplete();
  }



  @override
  void initState() {
    super.initState();
    // Trigger initial load if kitties list is empty
    if (userController.kitties.isEmpty &&
        !userController.kittiesLoading.value) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        userController.getUserkitties(page: 0);
      });
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Handle search with API
  void _handleSearch(String query) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      userController.searchKitties(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithImage(context),
      body: SmartRefresher(
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        enablePullUp: true,
        controller: _refreshController,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(height: 10.h),
            // Header section with "My Kitties" title and search
            // Obx(
            //   () =>
            //    userController.kitties.isEmpty
            //   //  && !userController.kittiesLoading.value
            //       ? const SizedBox()
            // :
            Column(children: [
              Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: _buildRow(context),
              ),
              SizedBox(height: 24.h),
              Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        autofocus: false,
                        controller: searchController,
                        decoration: InputDecoration(
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: Obx(() => userController.searchLoading.value
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: Padding(
                                    padding: EdgeInsets.all(12.0),
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  ),
                                )
                              : (searchController.text.isNotEmpty
                                  ? IconButton(
                                      icon: const Icon(Icons.clear),
                                      onPressed: () {
                                        searchController.clear();
                                        userController.searchKitties('');
                                      })
                                  : const SizedBox())),
                          hintText: "Search for a Kitty",
                        ),
                        onChanged: _handleSearch,
                      ),
                    ),  // Filter button
              IconButton(
                onPressed: _showFilterDialog,
                icon: Icon(
                  Icons.filter_alt_outlined,
                  color: currentFilters.isNotEmpty ? theme.colorScheme.primary : null,
                ),
                tooltip: 'Filter Kitties',
              ),
                  ],
                ),
              )
            ]),
            // ),

            // Main content area with kitties list or loading state
            Expanded(
              child: Obx(() {
                // Handle search loading state
                if (userController.searchLoading.value) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SpinKitDualRing(
                          color: ColorUtil.blueColor,
                          lineWidth: 4.sp,
                          size: 40.0.sp,
                        ),
                        const Text(
                          "Searching kitties...",
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        )
                      ],
                    ),
                  );
                }

                // Handle prefetching indicator (optional, shows loading state during prefetch)
                Widget? prefetchIndicator;
                if (userController.isPrefetching.value &&
                    !userController.loadingMore.value) {
                  prefetchIndicator = Positioned(
                    bottom: 10,
                    right: 10,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 15,
                            height: 15,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            "Prefetching...",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                // Handle main loading state
                if (userController.kittiesLoading.value &&
                    userController.kitties.isEmpty) {
                  return SizedBox(
                    height: SizeConfig.screenHeight * .33,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitDualRing(
                            color: ColorUtil.blueColor,
                            lineWidth: 4.sp,
                            size: 40.0.sp,
                          ),
                          const Text(
                            "loading kitties...",
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                    ),
                  );
                }

                // Handle empty state - no kitties at all
                if (userController.kitties.isEmpty &&
                    !userController.kittiesLoading.value) {
                  if (userController.searchQuery.isNotEmpty) {
                    // No kitties found for search query
                    return Center(
                      child: Padding(
                        padding: EdgeInsets.only(top: 15.h),
                        child: Text(
                          "No kitties found for '${userController.searchQuery.value}'",
                          style: const TextStyle(
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    );
                  }
                  // No kitties at all - show create kitty page
                  return const CrtContributionKittyPage();
                }

                // Show kitties list
                return Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 20),
                      child: Column(
                        children: [
                          // List of kitties
                          Expanded(
                            child: ListView.separated(
                              controller: userController.scrollController,
                              physics: const BouncingScrollPhysics(),
                              keyboardDismissBehavior:
                                  ScrollViewKeyboardDismissBehavior.onDrag,
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: 24.h),
                              itemCount: userController.kitties.length +
                                  ((userController.loadingMore.isTrue ||
                                          (userController.isLastPage.isTrue &&
                                              !userController
                                                  .loadingMore.value &&
                                              userController
                                                  .searchQuery.isEmpty))
                                      ? 1
                                      : 0),
                              itemBuilder: (context, index) {
                                // If this is the last item and we need to show loader or end message
                                if (index == userController.kitties.length) {
                                  // Show loading indicator
                                  if (userController.loadingMore.isTrue) {
                                    return const Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 16.0),
                                      child: Column(
                                        children: [
                                          SpinKitDualRing(
                                            color: ColorUtil.blueColor,
                                            lineWidth: 4,
                                            size: 40.0,
                                          ),
                                          Text(
                                            "loading more kitties...",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  // Show end of list indicator
                                  if (userController.isLastPage.isTrue &&
                                      !userController.loadingMore.value &&
                                      userController.searchQuery.isEmpty) {
                                    return const Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 16.0),
                                      child: Text(
                                        "No more kitties to load",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    );
                                  }
                                  // Fallback empty widget in case conditions don't match
                                  return const SizedBox();
                                }

                                // Regular kitty item
                                final kitty = userController.kitties[index];
                                return ContributionKittyWidget(kitty: kitty);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Show prefetch indicator if needed
                    if (prefetchIndicator != null) prefetchIndicator,
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildCreateAKittyButton(BuildContext context) {
    return CustomElevatedButton(
      isDisabled: false,
      onPressed: () {
        Get.toNamed(NavRoutes.createkittyScreen);
      },
      width: 160.w,
      height: 40.h,
      text: "Create a Kitty",
      buttonTextStyle: TextStyle(
        fontSize: 12.h,
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      leftIcon: Container(
        margin: EdgeInsets.only(right: 8.w),
        child: CustomImageView(
          imagePath: AssetUrl.imgPlus,
          height: 18.h,
          width: 18.w,
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 2.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: 8.h,
              bottom: 8.h,
            ),
            child: Text(
              "My Kitties",
              style: theme.textTheme.titleLarge,
              // ignore: deprecated_member_use
              textScaleFactor: 0.8,
            ),
          ),
          Row(
            children: [ 
              _buildCreateAKittyButton(context),
            ],
          ),
        ],
      ),
    );
  }
}
